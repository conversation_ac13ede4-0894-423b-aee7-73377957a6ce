{"name": "isotope-ai-mobile", "version": "1.0.0", "main": "index.ts", "type": "module", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/blur": "^4.4.1", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/push-notification-ios": "^1.11.0", "@react-native-google-signin/google-signin": "^14.0.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/native-stack": "^6.11.0", "@react-navigation/stack": "^7.3.4", "@shopify/react-native-skia": "^2.0.4", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.7", "expo": "~53.0.11", "expo-av": "^14.0.7", "expo-background-fetch": "^12.0.1", "expo-document-picker": "^13.1.5", "expo-file-system": "^18.1.10", "expo-notifications": "^0.31.3", "expo-status-bar": "~2.2.3", "expo-task-manager": "^11.8.2", "lottie-react-native": "^7.2.2", "material-dynamic-colors": "^1.1.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-elements": "^3.4.3", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.24.0", "react-native-linear-gradient": "^2.8.3", "react-native-material-ripple": "^0.9.1", "react-native-material-you-colors": "^0.1.2", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-shared-element": "^0.8.4", "react-native-super-grid": "^6.0.1", "react-native-svg": "^15.12.0", "react-native-ui-lib": "^7.43.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "victory-native": "^41.17.4", "zustand": "^4.5.7"}, "devDependencies": {"@babel/core": "^7.25.2", "@eslint/js": "^9.29.0", "@types/react": "~19.0.10", "eslint": "^9.29.0", "prettier": "^3.5.3", "typescript": "~5.8.3"}, "private": true}