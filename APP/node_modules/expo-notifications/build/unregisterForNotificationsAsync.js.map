{"version": 3, "file": "unregisterForNotificationsAsync.js", "sourceRoot": "", "sources": ["../src/unregisterForNotificationsAsync.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAExD,OAAO,gBAAgB,MAAM,oBAAoB,CAAC;AAElD,eAAe;AACf,MAAM,CAAC,OAAO,CAAC,KAAK,UAAU,+BAA+B;IAC3D,IAAI,CAAC,gBAAgB,CAAC,+BAA+B,EAAE,CAAC;QACtD,MAAM,IAAI,mBAAmB,CAAC,mBAAmB,EAAE,iCAAiC,CAAC,CAAC;IACxF,CAAC;IACD,OAAO,gBAAgB,CAAC,+BAA+B,EAAE,CAAC;AAC5D,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\n\nimport PushTokenManager from './PushTokenManager';\n\n// @docsMissing\nexport default async function unregisterForNotificationsAsync(): Promise<void> {\n  if (!PushTokenManager.unregisterForNotificationsAsync) {\n    throw new UnavailabilityError('ExpoNotifications', 'unregisterForNotificationsAsync');\n  }\n  return PushTokenManager.unregisterForNotificationsAsync();\n}\n"]}