import React from "react";

import { checkImage } from "../../../__tests__/setup";
import { Fill, Group, TextPath, fitbox } from "../../components";
import { fonts, importSkia, surface } from "../setup";

describe("Text Paths", () => {
  // The NotoColorEmoji font is not supported on iOS
  it("Should draw text along a path", async () => {
    const { Skia, rect, processTransform2d } = importSkia();
    const circlePath = Skia.Path.MakeFromSVGString(
      // eslint-disable-next-line max-len
      "M332 172Q332 175.928 331.807 179.851Q331.615 183.774 331.23 187.683Q330.845 191.592 330.268 195.477Q329.692 199.362 328.926 203.214Q328.159 207.067 327.205 210.877Q326.251 214.687 325.11 218.446Q323.97 222.204 322.647 225.902Q321.324 229.601 319.821 233.229Q318.318 236.858 316.638 240.409Q314.959 243.959 313.107 247.423Q311.256 250.887 309.237 254.256Q307.217 257.625 305.035 260.891Q302.853 264.157 300.513 267.312Q298.173 270.467 295.682 273.503Q293.19 276.539 290.552 279.449Q287.914 282.36 285.137 285.137Q282.36 287.914 279.449 290.552Q276.539 293.19 273.503 295.682Q270.467 298.173 267.312 300.513Q264.157 302.853 260.891 305.035Q257.625 307.217 254.256 309.237Q250.887 311.256 247.423 313.107Q243.959 314.959 240.409 316.638Q236.858 318.318 233.229 319.821Q229.601 321.324 225.902 322.647Q222.204 323.97 218.446 325.11Q214.687 326.251 210.877 327.205Q207.067 328.159 203.214 328.926Q199.362 329.692 195.477 330.268Q191.592 330.845 187.683 331.23Q183.774 331.615 179.851 331.807Q175.928 332 172 332Q168.072 332 164.149 331.807Q160.226 331.615 156.317 331.23Q152.408 330.845 148.523 330.268Q144.638 329.692 140.786 328.926Q136.933 328.159 133.123 327.205Q129.313 326.251 125.554 325.11Q121.796 323.97 118.098 322.647Q114.399 321.324 110.771 319.821Q107.142 318.318 103.591 316.638Q100.041 314.959 96.5765 313.107Q93.1125 311.256 89.7435 309.237Q86.3746 307.217 83.1087 305.035Q79.8429 302.853 76.6881 300.513Q73.5333 298.173 70.4971 295.682Q67.4609 293.19 64.5506 290.552Q61.6403 287.914 58.8629 285.137Q56.0855 282.36 53.4478 279.449Q50.8101 276.539 48.3183 273.503Q45.8266 270.467 43.4868 267.312Q41.147 264.157 38.9649 260.891Q36.7827 257.625 34.7634 254.256Q32.7441 250.887 30.8926 247.423Q29.0411 243.959 27.3617 240.409Q25.6824 236.858 24.1793 233.229Q22.6762 229.601 21.3529 225.902Q20.0297 222.204 18.8895 218.446Q17.7494 214.687 16.795 210.877Q15.8406 207.067 15.0744 203.214Q14.3081 199.362 13.7318 195.477Q13.1554 191.592 12.7704 187.683Q12.3855 183.774 12.1927 179.851Q12 175.928 12 172Q12 168.072 12.1927 164.149Q12.3855 160.226 12.7704 156.317Q13.1554 152.408 13.7318 148.523Q14.3081 144.638 15.0744 140.786Q15.8406 136.933 16.795 133.123Q17.7494 129.313 18.8895 125.554Q20.0297 121.796 21.3529 118.098Q22.6762 114.399 24.1793 110.771Q25.6824 107.142 27.3617 103.591Q29.0411 100.041 30.8926 96.5765Q32.7441 93.1125 34.7634 89.7435Q36.7827 86.3746 38.9649 83.1087Q41.147 79.8429 43.4868 76.6881Q45.8266 73.5333 48.3183 70.4971Q50.8101 67.4609 53.4478 64.5506Q56.0855 61.6403 58.8629 58.8629Q61.6403 56.0855 64.5506 53.4478Q67.4609 50.8101 70.4971 48.3183Q73.5333 45.8266 76.6881 43.4868Q79.8429 41.147 83.1087 38.9649Q86.3746 36.7827 89.7435 34.7634Q93.1125 32.7441 96.5765 30.8926Q100.041 29.0411 103.591 27.3617Q107.142 25.6824 110.771 24.1793Q114.399 22.6762 118.098 21.3529Q121.796 20.0297 125.554 18.8895Q129.313 17.7494 133.123 16.795Q136.933 15.8406 140.786 15.0744Q144.638 14.3081 148.523 13.7318Q152.408 13.1554 156.317 12.7704Q160.226 12.3855 164.149 12.1927Q168.072 12 172 12Q175.928 12 179.851 12.1927Q183.774 12.3855 187.683 12.7704Q191.592 13.1554 195.477 13.7318Q199.362 14.3081 203.214 15.0744Q207.067 15.8406 210.877 16.795Q214.687 17.7494 218.446 18.8895Q222.204 20.0297 225.902 21.3529Q229.601 22.6762 233.229 24.1793Q236.858 25.6824 240.409 27.3617Q243.959 29.0411 247.423 30.8926Q250.887 32.7441 254.256 34.7634Q257.625 36.7827 260.891 38.9649Q264.157 41.147 267.312 43.4868Q270.467 45.8266 273.503 48.3183Q276.539 50.8101 279.449 53.4478Q282.36 56.0855 285.137 58.8629Q287.914 61.6403 290.552 64.5506Q293.19 67.4609 295.682 70.4971Q298.173 73.5333 300.513 76.6881Q302.853 79.8429 305.035 83.1087Q307.217 86.3746 309.237 89.7435Q311.256 93.1125 313.107 96.5765Q314.959 100.041 316.638 103.591Q318.318 107.142 319.821 110.771Q321.324 114.399 322.647 118.098Q323.97 121.796 325.11 125.554Q326.251 129.313 327.205 133.123Q328.159 136.933 328.926 140.786Q329.692 144.638 330.268 148.523Q330.845 152.408 331.23 156.317Q331.615 160.226 331.807 164.149Q332 168.072 332 172Z"
    )!;

    const reversedPath = Skia.Path.MakeFromSVGString(
      // eslint-disable-next-line max-len
      "M 344 172 Q 344 167.778 343.793 163.56 Q 343.586 159.343 343.172 155.141 Q 342.758 150.939 342.138 146.762 Q 341.519 142.586 340.695 138.444 Q 339.871 134.303 338.845 130.207 Q 337.819 126.112 336.594 122.071 Q 335.368 118.03 333.946 114.055 Q 332.523 110.079 330.907 106.178 Q 329.291 102.277 327.486 98.4605 Q 325.681 94.6435 323.69 90.9198 Q 321.7 87.196 319.529 83.5743 Q 317.359 79.9527 315.013 76.4419 Q 312.667 72.9311 310.152 69.5397 Q 307.636 66.1483 304.958 62.8844 Q 302.279 59.6204 299.444 56.4919 Q 296.608 53.3633 293.622 50.3776 Q 290.637 47.392 287.508 44.5564 Q 284.38 41.7208 281.116 39.0422 Q 277.852 36.3636 274.46 33.8483 Q 271.069 31.333 267.558 28.9872 Q 264.047 26.6414 260.426 24.4707 Q 256.804 22.2999 253.08 20.3095 Q 249.356 18.3191 245.539 16.5138 Q 241.722 14.7085 237.822 13.0927 Q 233.921 11.4769 229.945 10.0544 Q 225.969 8.63195 221.929 7.40626 Q 217.888 6.18057 213.793 5.15462 Q 209.697 4.12867 205.556 3.30493 Q 201.414 2.48119 197.238 1.86164 Q 193.061 1.24209 188.859 0.828227 Q 184.657 0.414363 180.44 0.207182 Q 176.222 0 172 0 Q 167.778 0 163.56 0.207182 Q 159.343 0.414363 155.141 0.828227 Q 150.939 1.24209 146.762 1.86164 Q 142.586 2.48119 138.444 3.30493 Q 134.303 4.12867 130.207 5.15462 Q 126.112 6.18057 122.071 7.40626 Q 118.03 8.63195 114.055 10.0544 Q 110.079 11.4769 106.178 13.0927 Q 102.277 14.7085 98.4605 16.5138 Q 94.6435 18.3191 90.9198 20.3095 Q 87.196 22.2999 83.5743 24.4707 Q 79.9527 26.6414 76.4419 28.9872 Q 72.9311 31.333 69.5397 33.8483 Q 66.1483 36.3636 62.8844 39.0422 Q 59.6204 41.7208 56.4919 44.5564 Q 53.3633 47.392 50.3776 50.3776 Q 47.392 53.3633 44.5564 56.4919 Q 41.7208 59.6204 39.0422 62.8844 Q 36.3636 66.1483 33.8483 69.5397 Q 31.333 72.9311 28.9872 76.4419 Q 26.6414 79.9527 24.4707 83.5743 Q 22.2999 87.196 20.3095 90.9198 Q 18.3191 94.6435 16.5138 98.4605 Q 14.7085 102.277 13.0927 106.178 Q 11.4769 110.079 10.0544 114.055 Q 8.63195 118.03 7.40626 122.071 Q 6.18057 126.112 5.15462 130.207 Q 4.12867 134.303 3.30493 138.444 Q 2.48119 142.586 1.86164 146.762 Q 1.24209 150.939 0.828227 155.141 Q 0.414363 159.343 0.207182 163.56 Q 0 167.778 0 172 Q 0 176.222 0.207182 180.44 Q 0.414363 184.657 0.828227 188.859 Q 1.24209 193.061 1.86164 197.238 Q 2.48119 201.414 3.30493 205.556 Q 4.12867 209.697 5.15462 213.793 Q 6.18057 217.888 7.40626 221.929 Q 8.63195 225.969 10.0544 229.945 Q 11.4769 233.921 13.0927 237.822 Q 14.7085 241.722 16.5138 245.539 Q 18.3191 249.356 20.3095 253.08 Q 22.2999 256.804 24.4707 260.426 Q 26.6414 264.047 28.9872 267.558 Q 31.333 271.069 33.8483 274.46 Q 36.3636 277.852 39.0422 281.116 Q 41.7208 284.38 44.5564 287.508 Q 47.392 290.637 50.3776 293.622 Q 53.3633 296.608 56.4919 299.444 Q 59.6204 302.279 62.8844 304.958 Q 66.1483 307.636 69.5397 310.152 Q 72.9311 312.667 76.4419 315.013 Q 79.9527 317.359 83.5743 319.529 Q 87.196 321.7 90.9198 323.69 Q 94.6435 325.681 98.4605 327.486 Q 102.277 329.291 106.178 330.907 Q 110.079 332.523 114.055 333.946 Q 118.03 335.368 122.071 336.594 Q 126.112 337.819 130.207 338.845 Q 134.303 339.871 138.444 340.695 Q 142.586 341.519 146.762 342.138 Q 150.939 342.758 155.141 343.172 Q 159.343 343.586 163.56 343.793 Q 167.778 344 172 344 Q 176.222 344 180.44 343.793 Q 184.657 343.586 188.859 343.172 Q 193.061 342.758 197.238 342.138 Q 201.414 341.519 205.556 340.695 Q 209.697 339.871 213.793 338.845 Q 217.888 337.819 221.929 336.594 Q 225.969 335.368 229.945 333.946 Q 233.921 332.523 237.822 330.907 Q 241.722 329.291 245.539 327.486 Q 249.356 325.681 253.08 323.69 Q 256.804 321.7 260.426 319.529 Q 264.047 317.359 267.558 315.013 Q 271.069 312.667 274.46 310.152 Q 277.852 307.636 281.116 304.958 Q 284.38 302.279 287.508 299.444 Q 290.637 296.608 293.622 293.622 Q 296.608 290.637 299.444 287.508 Q 302.279 284.38 304.958 281.116 Q 307.636 277.852 310.152 274.46 Q 312.667 271.069 315.013 267.558 Q 317.359 264.047 319.529 260.426 Q 321.7 256.804 323.69 253.08 Q 325.681 249.356 327.486 245.539 Q 329.291 241.722 330.907 237.822 Q 332.523 233.921 333.946 229.945 Q 335.368 225.969 336.594 221.929 Q 337.819 217.888 338.845 213.793 Q 339.871 209.697 340.695 205.556 Q 341.519 201.414 342.138 197.238 Q 342.758 193.061 343.172 188.859 Q 343.586 184.657 343.793 180.44 Q 344 176.222 344 172 Z"
    )!;
    const font = fonts.UberMoveMediumMono;
    const dst = rect(16, 16, surface.width - 32, surface.height - 32);
    circlePath.transform(
      processTransform2d(
        fitbox("contain", circlePath.computeTightBounds(), dst)
      )
    );
    reversedPath.transform(
      processTransform2d(
        fitbox("contain", reversedPath.computeTightBounds(), dst)
      )
    );
    const image = await surface.draw(
      <>
        <Fill color="white" />
        <Group>
          <TextPath font={font} path={circlePath} text="5 Kts" />
        </Group>
        <Group>
          <TextPath font={font} path={reversedPath} text="5 Kts" />
        </Group>
      </>
    );
    checkImage(image, `snapshots/text/text-path-bug-${surface.OS}.png`);
  });
});
