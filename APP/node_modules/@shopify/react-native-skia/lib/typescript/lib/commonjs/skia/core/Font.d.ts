export const __esModule: boolean;
/**
 * Returns a Skia Font object
 * */
export function useFont(font: any, size: number | undefined, onError: any): import("../../../..").SkFont | null;
export function matchFont(inputStyle?: {}, fontMgr?: import("../../../..").SkFontMgr): import("../../../..").SkFont;
export function listFontFamilies(fontMgr?: import("../../../..").SkFontMgr): string[];
export function useFonts(sources: any): null;
