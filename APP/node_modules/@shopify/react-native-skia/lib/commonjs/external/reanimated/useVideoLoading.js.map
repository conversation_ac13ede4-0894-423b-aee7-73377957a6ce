{"version": 3, "names": ["_react", "require", "_skia", "_ReanimatedProxy", "_interopRequireDefault", "e", "__esModule", "default", "runtime", "<PERSON><PERSON>", "createWorkletRuntime", "useVideoLoading", "source", "runOnJS", "video", "setVideo", "useState", "cb", "useCallback", "src", "vid", "Skia", "Video", "useEffect", "runOnRuntime", "exports"], "sources": ["useVideoLoading.ts"], "sourcesContent": ["import { useCallback, useEffect, useState } from \"react\";\n\nimport type { Video } from \"../../skia/types\";\nimport { Skia } from \"../../skia\";\n\nimport Rea from \"./ReanimatedProxy\";\n\nconst runtime = Rea.createWorkletRuntime(\"video-metadata-runtime\");\n\ntype VideoSource = string | null;\n\nexport const useVideoLoading = (source: VideoSource) => {\n  const { runOnJS } = Rea;\n  const [video, setVideo] = useState<Video | null>(null);\n  const cb = useCallback(\n    (src: string) => {\n      \"worklet\";\n      const vid = Skia.Video(src) as Video;\n      runOnJS(setVideo)(vid);\n    },\n    [runOnJS]\n  );\n  useEffect(() => {\n    if (source) {\n      Rea.runOnRuntime(runtime, cb)(source);\n    }\n  }, [cb, source]);\n  return video;\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,gBAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAoC,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,MAAMG,OAAO,GAAGC,wBAAG,CAACC,oBAAoB,CAAC,wBAAwB,CAAC;AAI3D,MAAMC,eAAe,GAAIC,MAAmB,IAAK;EACtD,MAAM;IAAEC;EAAQ,CAAC,GAAGJ,wBAAG;EACvB,MAAM,CAACK,KAAK,EAAEC,QAAQ,CAAC,GAAG,IAAAC,eAAQ,EAAe,IAAI,CAAC;EACtD,MAAMC,EAAE,GAAG,IAAAC,kBAAW,EACnBC,GAAW,IAAK;IACf,SAAS;;IACT,MAAMC,GAAG,GAAGC,UAAI,CAACC,KAAK,CAACH,GAAG,CAAU;IACpCN,OAAO,CAACE,QAAQ,CAAC,CAACK,GAAG,CAAC;EACxB,CAAC,EACD,CAACP,OAAO,CACV,CAAC;EACD,IAAAU,gBAAS,EAAC,MAAM;IACd,IAAIX,MAAM,EAAE;MACVH,wBAAG,CAACe,YAAY,CAAChB,OAAO,EAAES,EAAE,CAAC,CAACL,MAAM,CAAC;IACvC;EACF,CAAC,EAAE,CAACK,EAAE,EAAEL,MAAM,CAAC,CAAC;EAChB,OAAOE,KAAK;AACd,CAAC;AAACW,OAAA,CAAAd,eAAA,GAAAA,eAAA", "ignoreList": []}