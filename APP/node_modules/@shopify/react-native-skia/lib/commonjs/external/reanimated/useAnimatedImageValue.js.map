{"version": 3, "names": ["_AnimatedImage", "require", "_ReanimatedProxy", "_interopRequireDefault", "e", "__esModule", "default", "DEFAULT_FRAME_DURATION", "useAnimatedImageValue", "source", "paused", "defaultPaused", "<PERSON><PERSON>", "useSharedValue", "isPaused", "currentFrame", "lastTimestamp", "animatedImage", "useAnimatedImage", "err", "console", "error", "Error", "message", "frameDuration", "currentFrameDuration", "useFrameCallback", "frameInfo", "value", "timestamp", "elapsed", "decodeNextFrame", "old<PERSON><PERSON><PERSON>", "getCurrentFrame", "dispose", "exports"], "sources": ["useAnimatedImageValue.ts"], "sourcesContent": ["import type { FrameInfo, SharedValue } from \"react-native-reanimated\";\n\nimport { useAnimatedImage } from \"../../skia/core/AnimatedImage\";\nimport type { DataSourceParam, SkImage } from \"../../skia/types\";\n\nimport Rea from \"./ReanimatedProxy\";\n\nconst DEFAULT_FRAME_DURATION = 60;\n\nexport const useAnimatedImageValue = (\n  source: DataSourceParam,\n  paused?: SharedValue<boolean>\n) => {\n  const defaultPaused = Rea.useSharedValue(false);\n  const isPaused = paused ?? defaultPaused;\n  const currentFrame = Rea.useSharedValue<null | SkImage>(null);\n  const lastTimestamp = Rea.useSharedValue(-1);\n  const animatedImage = useAnimatedImage(source, (err) => {\n    console.error(err);\n    throw new Error(`Could not load animated image - got '${err.message}'`);\n  });\n  const frameDuration =\n    animatedImage?.currentFrameDuration() || DEFAULT_FRAME_DURATION;\n\n  Rea.useFrameCallback((frameInfo: FrameInfo) => {\n    if (!animatedImage) {\n      currentFrame.value = null;\n      return;\n    }\n    if (isPaused.value && lastTimestamp.value !== -1) {\n      return;\n    }\n    const { timestamp } = frameInfo;\n    const elapsed = timestamp - lastTimestamp.value;\n\n    // Check if it's time to switch frames based on GIF frame duration\n    if (elapsed < frameDuration) {\n      return;\n    }\n\n    // Update the current frame\n    animatedImage.decodeNextFrame();\n    const oldFrame = currentFrame.value;\n    currentFrame.value = animatedImage.getCurrentFrame();\n    if (oldFrame) {\n      oldFrame.dispose();\n    }\n\n    // Update the last timestamp\n    lastTimestamp.value = timestamp;\n  });\n  return currentFrame;\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAGA,IAAAC,gBAAA,GAAAC,sBAAA,CAAAF,OAAA;AAAoC,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,MAAMG,sBAAsB,GAAG,EAAE;AAE1B,MAAMC,qBAAqB,GAAGA,CACnCC,MAAuB,EACvBC,MAA6B,KAC1B;EACH,MAAMC,aAAa,GAAGC,wBAAG,CAACC,cAAc,CAAC,KAAK,CAAC;EAC/C,MAAMC,QAAQ,GAAGJ,MAAM,aAANA,MAAM,cAANA,MAAM,GAAIC,aAAa;EACxC,MAAMI,YAAY,GAAGH,wBAAG,CAACC,cAAc,CAAiB,IAAI,CAAC;EAC7D,MAAMG,aAAa,GAAGJ,wBAAG,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMI,aAAa,GAAG,IAAAC,+BAAgB,EAACT,MAAM,EAAGU,GAAG,IAAK;IACtDC,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;IAClB,MAAM,IAAIG,KAAK,CAAC,wCAAwCH,GAAG,CAACI,OAAO,GAAG,CAAC;EACzE,CAAC,CAAC;EACF,MAAMC,aAAa,GACjB,CAAAP,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEQ,oBAAoB,CAAC,CAAC,KAAIlB,sBAAsB;EAEjEK,wBAAG,CAACc,gBAAgB,CAAEC,SAAoB,IAAK;IAC7C,IAAI,CAACV,aAAa,EAAE;MAClBF,YAAY,CAACa,KAAK,GAAG,IAAI;MACzB;IACF;IACA,IAAId,QAAQ,CAACc,KAAK,IAAIZ,aAAa,CAACY,KAAK,KAAK,CAAC,CAAC,EAAE;MAChD;IACF;IACA,MAAM;MAAEC;IAAU,CAAC,GAAGF,SAAS;IAC/B,MAAMG,OAAO,GAAGD,SAAS,GAAGb,aAAa,CAACY,KAAK;;IAE/C;IACA,IAAIE,OAAO,GAAGN,aAAa,EAAE;MAC3B;IACF;;IAEA;IACAP,aAAa,CAACc,eAAe,CAAC,CAAC;IAC/B,MAAMC,QAAQ,GAAGjB,YAAY,CAACa,KAAK;IACnCb,YAAY,CAACa,KAAK,GAAGX,aAAa,CAACgB,eAAe,CAAC,CAAC;IACpD,IAAID,QAAQ,EAAE;MACZA,QAAQ,CAACE,OAAO,CAAC,CAAC;IACpB;;IAEA;IACAlB,aAAa,CAACY,KAAK,GAAGC,SAAS;EACjC,CAAC,CAAC;EACF,OAAOd,YAAY;AACrB,CAAC;AAACoB,OAAA,CAAA3B,qBAAA,GAAAA,qBAAA", "ignoreList": []}