{"version": 3, "names": ["_react", "require", "_Platform", "_ReanimatedProxy", "_interopRequireDefault", "_useVideoLoading", "e", "__esModule", "default", "copyFrameOnAndroid", "currentFrame", "Platform", "OS", "tex", "value", "makeNonTextureImage", "dispose", "set<PERSON>rame", "video", "img", "nextImage", "defaultOptions", "looping", "paused", "seek", "currentTime", "volume", "useOption", "defaultValue", "<PERSON><PERSON>", "useSharedValue", "isSharedValue", "disposeVideo", "useVideo", "source", "userOptions", "_userOptions$paused", "_userOptions$looping", "_userOptions$seek", "_userOptions$volume", "useVideoLoading", "isPaused", "lastTimestamp", "duration", "useMemo", "_video$duration", "framerate", "_video$framerate", "size", "_video$size", "width", "height", "rotation", "_video$rotation", "frameDuration", "currentFrameDuration", "Math", "floor", "useAnimatedReaction", "pause", "play", "setVolume", "useFrameCallback", "frameInfo", "currentTimestamp", "timestamp", "delta", "isOver", "useEffect", "runOnUI", "exports"], "sources": ["useVideo.ts"], "sourcesContent": ["import type { SharedValue, FrameInfo } from \"react-native-reanimated\";\nimport { useEffect, useMemo } from \"react\";\n\nimport type { SkImage, Video } from \"../../skia/types\";\nimport { Platform } from \"../../Platform\";\n\nimport Rea from \"./ReanimatedProxy\";\nimport { useVideoLoading } from \"./useVideoLoading\";\n\ntype MaybeAnimated<T> = SharedValue<T> | T;\n\ninterface PlaybackOptions {\n  looping: MaybeAnimated<boolean>;\n  paused: MaybeAnimated<boolean>;\n  seek: MaybeAnimated<number | null>;\n  volume: MaybeAnimated<number>;\n}\n\nconst copyFrameOnAndroid = (currentFrame: SharedValue<SkImage | null>) => {\n  \"worklet\";\n  // on android we need to copy the texture before it's invalidated\n  if (Platform.OS === \"android\") {\n    const tex = currentFrame.value;\n    if (tex) {\n      currentFrame.value = tex.makeNonTextureImage();\n      tex.dispose();\n    }\n  }\n};\n\nconst setFrame = (video: Video, currentFrame: SharedValue<SkImage | null>) => {\n  \"worklet\";\n  const img = video.nextImage();\n  if (img) {\n    if (currentFrame.value) {\n      currentFrame.value.dispose();\n    }\n    currentFrame.value = img;\n    copyFrameOnAndroid(currentFrame);\n  }\n};\n\nconst defaultOptions = {\n  looping: true,\n  paused: false,\n  seek: null,\n  currentTime: 0,\n  volume: 0,\n};\n\nconst useOption = <T>(value: MaybeAnimated<T>) => {\n  \"worklet\";\n  // TODO: only create defaultValue is needed (via makeMutable)\n  const defaultValue = Rea.useSharedValue(\n    Rea.isSharedValue(value) ? value.value : value\n  );\n  return Rea.isSharedValue(value)\n    ? (value as SharedValue<T>)\n    : (defaultValue as SharedValue<T>);\n};\n\nconst disposeVideo = (video: Video | null) => {\n  \"worklet\";\n  video?.dispose();\n};\n\nexport const useVideo = (\n  source: string | null,\n  userOptions?: Partial<PlaybackOptions>\n) => {\n  const video = useVideoLoading(source);\n  const isPaused = useOption(userOptions?.paused ?? defaultOptions.paused);\n  const looping = useOption(userOptions?.looping ?? defaultOptions.looping);\n  const seek = useOption(userOptions?.seek ?? defaultOptions.seek);\n  const volume = useOption(userOptions?.volume ?? defaultOptions.volume);\n  const currentFrame = Rea.useSharedValue<null | SkImage>(null);\n  const currentTime = Rea.useSharedValue(0);\n  const lastTimestamp = Rea.useSharedValue(-1);\n  const duration = useMemo(() => video?.duration() ?? 0, [video]);\n  const framerate = useMemo(\n    () => (Platform.OS === \"web\" ? -1 : video?.framerate() ?? 0),\n    [video]\n  );\n  const size = useMemo(() => video?.size() ?? { width: 0, height: 0 }, [video]);\n  const rotation = useMemo(() => video?.rotation() ?? 0, [video]);\n  const frameDuration = 1000 / framerate;\n  const currentFrameDuration = Math.floor(frameDuration);\n  Rea.useAnimatedReaction(\n    () => isPaused.value,\n    (paused) => {\n      if (paused) {\n        video?.pause();\n      } else {\n        lastTimestamp.value = -1;\n        video?.play();\n      }\n    }\n  );\n  Rea.useAnimatedReaction(\n    () => seek.value,\n    (value) => {\n      if (value !== null) {\n        video?.seek(value);\n        currentTime.value = value;\n        seek.value = null;\n      }\n    }\n  );\n  Rea.useAnimatedReaction(\n    () => volume.value,\n    (value) => {\n      video?.setVolume(value);\n    }\n  );\n  Rea.useFrameCallback((frameInfo: FrameInfo) => {\n    \"worklet\";\n    if (!video) {\n      return;\n    }\n    if (isPaused.value) {\n      return;\n    }\n    const currentTimestamp = frameInfo.timestamp;\n    if (lastTimestamp.value === -1) {\n      lastTimestamp.value = currentTimestamp;\n    }\n    const delta = currentTimestamp - lastTimestamp.value;\n\n    const isOver = currentTime.value + delta > duration;\n    if (isOver && looping.value) {\n      seek.value = 0;\n      currentTime.value = seek.value;\n      lastTimestamp.value = currentTimestamp;\n    }\n    // On Web the framerate is uknown.\n    // This could be optimized by using requestVideoFrameCallback (Chrome only)\n    if ((delta >= currentFrameDuration && !isOver) || Platform.OS === \"web\") {\n      setFrame(video, currentFrame);\n      currentTime.value += delta;\n      lastTimestamp.value = currentTimestamp;\n    }\n  });\n\n  useEffect(() => {\n    return () => {\n      // TODO: should video simply be a shared value instead?\n      Rea.runOnUI(disposeVideo)(video);\n    };\n  }, [video]);\n\n  return {\n    currentFrame,\n    currentTime,\n    duration,\n    framerate,\n    rotation,\n    size,\n  };\n};\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,SAAA,GAAAD,OAAA;AAEA,IAAAE,gBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,gBAAA,GAAAJ,OAAA;AAAoD,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAWpD,MAAMG,kBAAkB,GAAIC,YAAyC,IAAK;EACxE,SAAS;;EACT;EACA,IAAIC,kBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;IAC7B,MAAMC,GAAG,GAAGH,YAAY,CAACI,KAAK;IAC9B,IAAID,GAAG,EAAE;MACPH,YAAY,CAACI,KAAK,GAAGD,GAAG,CAACE,mBAAmB,CAAC,CAAC;MAC9CF,GAAG,CAACG,OAAO,CAAC,CAAC;IACf;EACF;AACF,CAAC;AAED,MAAMC,QAAQ,GAAGA,CAACC,KAAY,EAAER,YAAyC,KAAK;EAC5E,SAAS;;EACT,MAAMS,GAAG,GAAGD,KAAK,CAACE,SAAS,CAAC,CAAC;EAC7B,IAAID,GAAG,EAAE;IACP,IAAIT,YAAY,CAACI,KAAK,EAAE;MACtBJ,YAAY,CAACI,KAAK,CAACE,OAAO,CAAC,CAAC;IAC9B;IACAN,YAAY,CAACI,KAAK,GAAGK,GAAG;IACxBV,kBAAkB,CAACC,YAAY,CAAC;EAClC;AACF,CAAC;AAED,MAAMW,cAAc,GAAG;EACrBC,OAAO,EAAE,IAAI;EACbC,MAAM,EAAE,KAAK;EACbC,IAAI,EAAE,IAAI;EACVC,WAAW,EAAE,CAAC;EACdC,MAAM,EAAE;AACV,CAAC;AAED,MAAMC,SAAS,GAAOb,KAAuB,IAAK;EAChD,SAAS;;EACT;EACA,MAAMc,YAAY,GAAGC,wBAAG,CAACC,cAAc,CACrCD,wBAAG,CAACE,aAAa,CAACjB,KAAK,CAAC,GAAGA,KAAK,CAACA,KAAK,GAAGA,KAC3C,CAAC;EACD,OAAOe,wBAAG,CAACE,aAAa,CAACjB,KAAK,CAAC,GAC1BA,KAAK,GACLc,YAA+B;AACtC,CAAC;AAED,MAAMI,YAAY,GAAId,KAAmB,IAAK;EAC5C,SAAS;;EACTA,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEF,OAAO,CAAC,CAAC;AAClB,CAAC;AAEM,MAAMiB,QAAQ,GAAGA,CACtBC,MAAqB,EACrBC,WAAsC,KACnC;EAAA,IAAAC,mBAAA,EAAAC,oBAAA,EAAAC,iBAAA,EAAAC,mBAAA;EACH,MAAMrB,KAAK,GAAG,IAAAsB,gCAAe,EAACN,MAAM,CAAC;EACrC,MAAMO,QAAQ,GAAGd,SAAS,EAAAS,mBAAA,GAACD,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEZ,MAAM,cAAAa,mBAAA,cAAAA,mBAAA,GAAIf,cAAc,CAACE,MAAM,CAAC;EACxE,MAAMD,OAAO,GAAGK,SAAS,EAAAU,oBAAA,GAACF,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEb,OAAO,cAAAe,oBAAA,cAAAA,oBAAA,GAAIhB,cAAc,CAACC,OAAO,CAAC;EACzE,MAAME,IAAI,GAAGG,SAAS,EAAAW,iBAAA,GAACH,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEX,IAAI,cAAAc,iBAAA,cAAAA,iBAAA,GAAIjB,cAAc,CAACG,IAAI,CAAC;EAChE,MAAME,MAAM,GAAGC,SAAS,EAAAY,mBAAA,GAACJ,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAET,MAAM,cAAAa,mBAAA,cAAAA,mBAAA,GAAIlB,cAAc,CAACK,MAAM,CAAC;EACtE,MAAMhB,YAAY,GAAGmB,wBAAG,CAACC,cAAc,CAAiB,IAAI,CAAC;EAC7D,MAAML,WAAW,GAAGI,wBAAG,CAACC,cAAc,CAAC,CAAC,CAAC;EACzC,MAAMY,aAAa,GAAGb,wBAAG,CAACC,cAAc,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAMa,QAAQ,GAAG,IAAAC,cAAO,EAAC;IAAA,IAAAC,eAAA;IAAA,QAAAA,eAAA,GAAM3B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,QAAQ,CAAC,CAAC,cAAAE,eAAA,cAAAA,eAAA,GAAI,CAAC;EAAA,GAAE,CAAC3B,KAAK,CAAC,CAAC;EAC/D,MAAM4B,SAAS,GAAG,IAAAF,cAAO,EACvB;IAAA,IAAAG,gBAAA;IAAA,OAAOpC,kBAAQ,CAACC,EAAE,KAAK,KAAK,GAAG,CAAC,CAAC,IAAAmC,gBAAA,GAAG7B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE4B,SAAS,CAAC,CAAC,cAAAC,gBAAA,cAAAA,gBAAA,GAAI,CAAC;EAAA,CAAC,EAC5D,CAAC7B,KAAK,CACR,CAAC;EACD,MAAM8B,IAAI,GAAG,IAAAJ,cAAO,EAAC;IAAA,IAAAK,WAAA;IAAA,QAAAA,WAAA,GAAM/B,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE8B,IAAI,CAAC,CAAC,cAAAC,WAAA,cAAAA,WAAA,GAAI;MAAEC,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EAAA,GAAE,CAACjC,KAAK,CAAC,CAAC;EAC7E,MAAMkC,QAAQ,GAAG,IAAAR,cAAO,EAAC;IAAA,IAAAS,eAAA;IAAA,QAAAA,eAAA,GAAMnC,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEkC,QAAQ,CAAC,CAAC,cAAAC,eAAA,cAAAA,eAAA,GAAI,CAAC;EAAA,GAAE,CAACnC,KAAK,CAAC,CAAC;EAC/D,MAAMoC,aAAa,GAAG,IAAI,GAAGR,SAAS;EACtC,MAAMS,oBAAoB,GAAGC,IAAI,CAACC,KAAK,CAACH,aAAa,CAAC;EACtDzB,wBAAG,CAAC6B,mBAAmB,CACrB,MAAMjB,QAAQ,CAAC3B,KAAK,EACnBS,MAAM,IAAK;IACV,IAAIA,MAAM,EAAE;MACVL,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEyC,KAAK,CAAC,CAAC;IAChB,CAAC,MAAM;MACLjB,aAAa,CAAC5B,KAAK,GAAG,CAAC,CAAC;MACxBI,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE0C,IAAI,CAAC,CAAC;IACf;EACF,CACF,CAAC;EACD/B,wBAAG,CAAC6B,mBAAmB,CACrB,MAAMlC,IAAI,CAACV,KAAK,EACfA,KAAK,IAAK;IACT,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBI,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEM,IAAI,CAACV,KAAK,CAAC;MAClBW,WAAW,CAACX,KAAK,GAAGA,KAAK;MACzBU,IAAI,CAACV,KAAK,GAAG,IAAI;IACnB;EACF,CACF,CAAC;EACDe,wBAAG,CAAC6B,mBAAmB,CACrB,MAAMhC,MAAM,CAACZ,KAAK,EACjBA,KAAK,IAAK;IACTI,KAAK,aAALA,KAAK,eAALA,KAAK,CAAE2C,SAAS,CAAC/C,KAAK,CAAC;EACzB,CACF,CAAC;EACDe,wBAAG,CAACiC,gBAAgB,CAAEC,SAAoB,IAAK;IAC7C,SAAS;;IACT,IAAI,CAAC7C,KAAK,EAAE;MACV;IACF;IACA,IAAIuB,QAAQ,CAAC3B,KAAK,EAAE;MAClB;IACF;IACA,MAAMkD,gBAAgB,GAAGD,SAAS,CAACE,SAAS;IAC5C,IAAIvB,aAAa,CAAC5B,KAAK,KAAK,CAAC,CAAC,EAAE;MAC9B4B,aAAa,CAAC5B,KAAK,GAAGkD,gBAAgB;IACxC;IACA,MAAME,KAAK,GAAGF,gBAAgB,GAAGtB,aAAa,CAAC5B,KAAK;IAEpD,MAAMqD,MAAM,GAAG1C,WAAW,CAACX,KAAK,GAAGoD,KAAK,GAAGvB,QAAQ;IACnD,IAAIwB,MAAM,IAAI7C,OAAO,CAACR,KAAK,EAAE;MAC3BU,IAAI,CAACV,KAAK,GAAG,CAAC;MACdW,WAAW,CAACX,KAAK,GAAGU,IAAI,CAACV,KAAK;MAC9B4B,aAAa,CAAC5B,KAAK,GAAGkD,gBAAgB;IACxC;IACA;IACA;IACA,IAAKE,KAAK,IAAIX,oBAAoB,IAAI,CAACY,MAAM,IAAKxD,kBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;MACvEK,QAAQ,CAACC,KAAK,EAAER,YAAY,CAAC;MAC7Be,WAAW,CAACX,KAAK,IAAIoD,KAAK;MAC1BxB,aAAa,CAAC5B,KAAK,GAAGkD,gBAAgB;IACxC;EACF,CAAC,CAAC;EAEF,IAAAI,gBAAS,EAAC,MAAM;IACd,OAAO,MAAM;MACX;MACAvC,wBAAG,CAACwC,OAAO,CAACrC,YAAY,CAAC,CAACd,KAAK,CAAC;IAClC,CAAC;EACH,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,OAAO;IACLR,YAAY;IACZe,WAAW;IACXkB,QAAQ;IACRG,SAAS;IACTM,QAAQ;IACRJ;EACF,CAAC;AACH,CAAC;AAACsB,OAAA,CAAArC,QAAA,GAAAA,QAAA", "ignoreList": []}