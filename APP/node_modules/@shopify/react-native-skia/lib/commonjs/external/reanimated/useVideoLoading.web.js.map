{"version": 3, "names": ["_react", "require", "_skia", "useVideoLoading", "source", "video", "setVideo", "useState", "useEffect", "vid", "Skia", "Video", "then", "v", "exports"], "sources": ["useVideoLoading.web.ts"], "sourcesContent": ["import { useEffect, useState } from \"react\";\n\nimport type { Video } from \"../../skia/types\";\nimport { Skia } from \"../../skia\";\n\ntype VideoSource = string | null;\n\nexport const useVideoLoading = (source: VideoSource) => {\n  const [video, setVideo] = useState<Video | null>(null);\n  useEffect(() => {\n    if (source) {\n      const vid = Skia.Video(source) as Promise<Video>;\n      vid.then((v) => setVideo(v));\n    }\n  }, [source]);\n  return video;\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAD,OAAA;AAIO,MAAME,eAAe,GAAIC,MAAmB,IAAK;EACtD,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG,IAAAC,eAAQ,EAAe,IAAI,CAAC;EACtD,IAAAC,gBAAS,EAAC,MAAM;IACd,IAAIJ,MAAM,EAAE;MACV,MAAMK,GAAG,GAAGC,UAAI,CAACC,KAAK,CAACP,MAAM,CAAmB;MAChDK,GAAG,CAACG,IAAI,CAAEC,CAAC,IAAKP,QAAQ,CAACO,CAAC,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAACT,MAAM,CAAC,CAAC;EACZ,OAAOC,KAAK;AACd,CAAC;AAACS,OAAA,CAAAX,eAAA,GAAAA,eAAA", "ignoreList": []}