{"version": 3, "names": ["_react", "require", "_Offscreen", "_skia", "_ReanimatedProxy", "_interopRequireDefault", "e", "__esModule", "default", "createTexture", "texture", "picture", "size", "value", "drawAsImageFromPicture", "useTexture", "element", "deps", "width", "height", "setPicture", "useState", "useEffect", "drawAsPicture", "x", "y", "then", "pic", "usePictureAsTexture", "exports", "<PERSON><PERSON>", "useSharedValue", "runOnUI", "useImageAsTexture", "source", "image", "useImage", "useMemo", "recorder", "Skia", "PictureRecorder", "canvas", "beginRecording", "drawImage", "finishRecordingAsPicture"], "sources": ["textures.tsx"], "sourcesContent": ["import { useEffect, useMemo, useState } from \"react\";\nimport type { DependencyList, ReactElement } from \"react\";\nimport type { SharedValue } from \"react-native-reanimated\";\n\nimport type {\n  DataSourceParam,\n  SkImage,\n  SkPicture,\n  SkSize,\n} from \"../../skia/types\";\nimport {\n  drawAsImageFromPicture,\n  drawAsPicture,\n} from \"../../renderer/Offscreen\";\nimport { Skia, useImage } from \"../../skia\";\n\nimport Rea from \"./ReanimatedProxy\";\n\nconst createTexture = (\n  texture: SharedValue<SkImage | null>,\n  picture: SkPicture,\n  size: SkSize\n) => {\n  \"worklet\";\n  texture.value = drawAsImageFromPicture(picture, size);\n};\n\nexport const useTexture = (\n  element: ReactElement,\n  size: SkSize,\n  deps?: DependencyList\n) => {\n  const { width, height } = size;\n  const [picture, setPicture] = useState<SkPicture | null>(null);\n  useEffect(() => {\n    drawAsPicture(element, {\n      x: 0,\n      y: 0,\n      width,\n      height,\n    }).then((pic) => {\n      setPicture(pic);\n    });\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, deps ?? []);\n  return usePictureAsTexture(picture, size);\n};\n\nexport const usePictureAsTexture = (\n  picture: SkPicture | null,\n  size: SkSize\n) => {\n  const texture = Rea.useSharedValue<SkImage | null>(null);\n  useEffect(() => {\n    if (picture !== null) {\n      Rea.runOnUI(createTexture)(texture, picture, size);\n    }\n  }, [picture, size, texture]);\n  return texture;\n};\n\nexport const useImageAsTexture = (source: DataSourceParam) => {\n  const image = useImage(source);\n  const size = useMemo(() => {\n    if (image) {\n      return { width: image.width(), height: image.height() };\n    }\n    return { width: 0, height: 0 };\n  }, [image]);\n  const picture = useMemo(() => {\n    if (image) {\n      const recorder = Skia.PictureRecorder();\n      const canvas = recorder.beginRecording({\n        x: 0,\n        y: 0,\n        width: size.width,\n        height: size.height,\n      });\n      canvas.drawImage(image, 0, 0);\n      return recorder.finishRecordingAsPicture();\n    } else {\n      return null;\n    }\n  }, [size, image]);\n  return usePictureAsTexture(picture, size);\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAUA,IAAAC,UAAA,GAAAD,OAAA;AAIA,IAAAE,KAAA,GAAAF,OAAA;AAEA,IAAAG,gBAAA,GAAAC,sBAAA,CAAAJ,OAAA;AAAoC,SAAAI,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEpC,MAAMG,aAAa,GAAGA,CACpBC,OAAoC,EACpCC,OAAkB,EAClBC,IAAY,KACT;EACH,SAAS;;EACTF,OAAO,CAACG,KAAK,GAAG,IAAAC,iCAAsB,EAACH,OAAO,EAAEC,IAAI,CAAC;AACvD,CAAC;AAEM,MAAMG,UAAU,GAAGA,CACxBC,OAAqB,EACrBJ,IAAY,EACZK,IAAqB,KAClB;EACH,MAAM;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGP,IAAI;EAC9B,MAAM,CAACD,OAAO,EAAES,UAAU,CAAC,GAAG,IAAAC,eAAQ,EAAmB,IAAI,CAAC;EAC9D,IAAAC,gBAAS,EAAC,MAAM;IACd,IAAAC,wBAAa,EAACP,OAAO,EAAE;MACrBQ,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE,CAAC;MACJP,KAAK;MACLC;IACF,CAAC,CAAC,CAACO,IAAI,CAAEC,GAAG,IAAK;MACfP,UAAU,CAACO,GAAG,CAAC;IACjB,CAAC,CAAC;IACF;EACF,CAAC,EAAEV,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,CAAC;EACd,OAAOW,mBAAmB,CAACjB,OAAO,EAAEC,IAAI,CAAC;AAC3C,CAAC;AAACiB,OAAA,CAAAd,UAAA,GAAAA,UAAA;AAEK,MAAMa,mBAAmB,GAAGA,CACjCjB,OAAyB,EACzBC,IAAY,KACT;EACH,MAAMF,OAAO,GAAGoB,wBAAG,CAACC,cAAc,CAAiB,IAAI,CAAC;EACxD,IAAAT,gBAAS,EAAC,MAAM;IACd,IAAIX,OAAO,KAAK,IAAI,EAAE;MACpBmB,wBAAG,CAACE,OAAO,CAACvB,aAAa,CAAC,CAACC,OAAO,EAAEC,OAAO,EAAEC,IAAI,CAAC;IACpD;EACF,CAAC,EAAE,CAACD,OAAO,EAAEC,IAAI,EAAEF,OAAO,CAAC,CAAC;EAC5B,OAAOA,OAAO;AAChB,CAAC;AAACmB,OAAA,CAAAD,mBAAA,GAAAA,mBAAA;AAEK,MAAMK,iBAAiB,GAAIC,MAAuB,IAAK;EAC5D,MAAMC,KAAK,GAAG,IAAAC,cAAQ,EAACF,MAAM,CAAC;EAC9B,MAAMtB,IAAI,GAAG,IAAAyB,cAAO,EAAC,MAAM;IACzB,IAAIF,KAAK,EAAE;MACT,OAAO;QAAEjB,KAAK,EAAEiB,KAAK,CAACjB,KAAK,CAAC,CAAC;QAAEC,MAAM,EAAEgB,KAAK,CAAChB,MAAM,CAAC;MAAE,CAAC;IACzD;IACA,OAAO;MAAED,KAAK,EAAE,CAAC;MAAEC,MAAM,EAAE;IAAE,CAAC;EAChC,CAAC,EAAE,CAACgB,KAAK,CAAC,CAAC;EACX,MAAMxB,OAAO,GAAG,IAAA0B,cAAO,EAAC,MAAM;IAC5B,IAAIF,KAAK,EAAE;MACT,MAAMG,QAAQ,GAAGC,UAAI,CAACC,eAAe,CAAC,CAAC;MACvC,MAAMC,MAAM,GAAGH,QAAQ,CAACI,cAAc,CAAC;QACrClB,CAAC,EAAE,CAAC;QACJC,CAAC,EAAE,CAAC;QACJP,KAAK,EAAEN,IAAI,CAACM,KAAK;QACjBC,MAAM,EAAEP,IAAI,CAACO;MACf,CAAC,CAAC;MACFsB,MAAM,CAACE,SAAS,CAACR,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;MAC7B,OAAOG,QAAQ,CAACM,wBAAwB,CAAC,CAAC;IAC5C,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF,CAAC,EAAE,CAAChC,IAAI,EAAEuB,KAAK,CAAC,CAAC;EACjB,OAAOP,mBAAmB,CAACjB,OAAO,EAAEC,IAAI,CAAC;AAC3C,CAAC;AAACiB,OAAA,CAAAI,iBAAA,GAAAA,iBAAA", "ignoreList": []}