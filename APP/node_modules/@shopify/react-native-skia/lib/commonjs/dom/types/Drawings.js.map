{"version": 3, "names": [], "sources": ["Drawings.ts"], "sourcesContent": ["import type {\n  FillType,\n  SkImage,\n  StrokeOpts,\n  Vector,\n  Color,\n  SkPoint,\n  BlendMode,\n  PointMode,\n  VertexMode,\n  SkFont,\n  SkRRect,\n  SkTextBlob,\n  SkPicture,\n  SkSVG,\n  SkPaint,\n  SkRect,\n  SkRSXform,\n  SkColor,\n  SamplingOptions,\n} from \"../../skia/types\";\n\nimport type {\n  CircleDef,\n  Fit,\n  GroupProps,\n  PathDef,\n  RectDef,\n  RRectDef,\n  SkEnum,\n} from \"./Common\";\n\nexport interface DrawingNodeProps extends GroupProps {\n  paint?: SkPaint;\n}\n\nexport type ImageProps = DrawingNodeProps &\n  RectDef & {\n    fit?: Fit;\n    image: SkImage | null;\n    sampling?: SamplingOptions;\n  };\n\nexport type CircleProps = CircleDef & DrawingNodeProps;\n\nexport interface PathProps extends DrawingNodeProps {\n  path: PathDef;\n  start: number;\n  end: number;\n  stroke?: StrokeOpts;\n  fillType?: SkEnum<typeof FillType>;\n}\n\nexport interface LineProps extends DrawingNodeProps {\n  p1: Vector;\n  p2: Vector;\n}\n\nexport type OvalProps = RectDef & DrawingNodeProps;\n\nexport type RectProps = RectDef & DrawingNodeProps;\n\nexport type RoundedRectProps = RRectDef & DrawingNodeProps;\n\nexport interface AtlasProps extends DrawingNodeProps {\n  image: SkImage | null;\n  sprites: SkRect[];\n  transforms: SkRSXform[];\n  colors?: SkColor[];\n  sampling?: SamplingOptions;\n}\n\nexport interface CubicBezierHandle {\n  pos: Vector;\n  c1: Vector;\n  c2: Vector;\n}\n\nexport interface PatchProps extends DrawingNodeProps {\n  colors?: Color[];\n  patch: [\n    CubicBezierHandle,\n    CubicBezierHandle,\n    CubicBezierHandle,\n    CubicBezierHandle\n  ];\n  texture?: readonly [SkPoint, SkPoint, SkPoint, SkPoint];\n  blendMode?: SkEnum<typeof BlendMode>;\n}\n\nexport interface VerticesProps extends DrawingNodeProps {\n  colors?: string[];\n  vertices: SkPoint[];\n  textures?: SkPoint[];\n  mode: SkEnum<typeof VertexMode>;\n  blendMode?: SkEnum<typeof BlendMode>;\n  indices?: number[];\n}\n\nexport interface ImageSVGProps extends DrawingNodeProps {\n  svg: SkSVG | null;\n  x?: number;\n  y?: number;\n  width?: number;\n  height?: number;\n  rect?: SkRect;\n}\n\nexport interface PictureProps extends DrawingNodeProps {\n  picture: SkPicture;\n}\n\nexport interface PointsProps extends DrawingNodeProps {\n  points: SkPoint[];\n  mode: SkEnum<typeof PointMode>;\n}\n\nexport interface DiffRectProps extends DrawingNodeProps {\n  inner: SkRRect;\n  outer: SkRRect;\n}\n\nexport interface TextProps extends DrawingNodeProps {\n  font: SkFont | null;\n  text: string;\n  x: number;\n  y: number;\n}\n\nexport interface TextPathProps extends DrawingNodeProps {\n  font: SkFont | null;\n  text: string;\n  path: PathDef;\n  initialOffset: number;\n}\n\nexport interface TextBlobProps extends DrawingNodeProps {\n  blob: SkTextBlob;\n  x: number;\n  y: number;\n}\n\nexport interface Glyph {\n  id: number;\n  pos: SkPoint;\n}\n\nexport interface GlyphsProps extends DrawingNodeProps {\n  font: SkFont | null;\n  x: number;\n  y: number;\n  glyphs: Glyph[];\n}\n\nexport interface BoxProps extends DrawingNodeProps {\n  box: SkRRect | SkRect;\n}\n\nexport interface BoxShadowProps {\n  dx?: number;\n  dy?: number;\n  spread?: number;\n  blur: number;\n  color?: Color;\n  inner?: boolean;\n}\n"], "mappings": "", "ignoreList": []}