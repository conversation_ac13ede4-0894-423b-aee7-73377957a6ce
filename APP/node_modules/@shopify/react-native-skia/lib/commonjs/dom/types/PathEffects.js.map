{"version": 3, "names": [], "sources": ["PathEffects.ts"], "sourcesContent": ["import type { Path1DEffectStyle, SkMatrix } from \"../../skia/types\";\n\nimport type { ChildrenProps, PathDef, SkEnum } from \"./Common\";\n\nexport interface CornerPathEffectProps extends ChildrenProps {\n  r: number;\n}\n\nexport interface DiscretePathEffectProps extends ChildrenProps {\n  length: number;\n  deviation: number;\n  seed: number;\n}\n\nexport interface DashPathEffectProps extends ChildrenProps {\n  intervals: number[];\n  phase?: number;\n}\n\nexport interface Path1DPathEffectProps extends ChildrenProps {\n  path: PathDef;\n  advance: number;\n  phase: number;\n  style: SkEnum<typeof Path1DEffectStyle>;\n}\n\nexport interface Path2DPathEffectProps extends ChildrenProps {\n  matrix: SkMatrix;\n  path: PathDef;\n}\n\nexport interface Line2DPathEffectProps extends ChildrenProps {\n  width: number;\n  matrix: SkMatrix;\n}\n"], "mappings": "", "ignoreList": []}