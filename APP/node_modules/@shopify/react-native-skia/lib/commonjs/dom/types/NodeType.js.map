{"version": 3, "names": ["NodeType", "exports"], "sources": ["NodeType.ts"], "sourcesContent": ["export const enum NodeType {\n  // Shaders\n  Layer = \"skLayer\",\n  Shader = \"skShader\",\n  ImageShader = \"skImageShader\",\n  ColorShader = \"skColorShader\",\n  Turbulence = \"skTurbulence\",\n  FractalNoise = \"skFractalN<PERSON>\",\n  LinearGradient = \"skLinearGradient\",\n  RadialGradient = \"skRadialGradient\",\n  SweepGradient = \"skSweepGradient\",\n  TwoPointConicalGradient = \"skTwoPointConicalGradient\",\n\n  // Mask Filters\n  BlurMaskFilter = \"skBlurMaskFilter\",\n\n  // Path Effects\n  DiscretePathEffect = \"skDiscretePathEffect\",\n  DashPathEffect = \"skDashPathEffect\",\n  Path1DPathEffect = \"skPath1DPathEffect\",\n  Path2DPathEffect = \"skPath2DPathEffect\",\n  CornerPathEffect = \"skCornerPathEffect\",\n  SumPathEffect = \"skSumPathEffect\",\n  Line2DPathEffect = \"skLine2DPathEffect\",\n\n  // Color Filters\n  MatrixColorFilter = \"skMatrixColorFilter\",\n  BlendColorFilter = \"skBlendColorFilter\",\n  LinearToSRGBGammaColorFilter = \"skLinearToSRGBGammaColorFilter\",\n  SRGBToLinearGammaColorFilter = \"skSRGBToLinearGammaColorFilter\",\n  LumaColorFilter = \"skLumaColorFilter\",\n  LerpColorFilter = \"skLerpColorFilter\",\n\n  // Image Filters\n  OffsetImageFilter = \"skOffsetImageFilter\",\n  DisplacementMapImageFilter = \"skDisplacementMapImageFilter\",\n  BlurImageFilter = \"skBlurImageFilter\",\n  DropShadowImageFilter = \"skDropShadowImageFilter\",\n  MorphologyImageFilter = \"skMorphologyImageFilter\",\n  BlendImageFilter = \"skBlendImageFilter\",\n  RuntimeShaderImageFilter = \"skRuntimeShaderImageFilter\",\n\n  // Mixed\n  Blend = \"skBlend\",\n  BackdropFilter = \"skBackdropFilter\",\n  Box = \"skBox\",\n  BoxShadow = \"skBoxShadow\",\n\n  // Drawings\n  Group = \"skGroup\",\n  Paint = \"skPaint\",\n  Circle = \"skCircle\",\n  Fill = \"skFill\",\n  Image = \"skImage\",\n  Points = \"skPoints\",\n  Path = \"skPath\",\n  Rect = \"skRect\",\n  RRect = \"skRRect\",\n  Oval = \"skOval\",\n  Line = \"skLine\",\n  Patch = \"skPatch\",\n  Vertices = \"skVertices\",\n  DiffRect = \"skDiffRect\",\n  Text = \"skText\",\n  TextPath = \"skTextPath\",\n  TextBlob = \"skTextBlob\",\n  Glyphs = \"skGlyphs\",\n  Picture = \"skPicture\",\n  ImageSVG = \"skImageSVG\",\n  Atlas = \"skAtlas\",\n\n  // Paragraph\n  Paragraph = \"skParagraph\",\n}\n"], "mappings": ";;;;;;IAAkBA,QAAQ,GAAAC,OAAA,CAAAD,QAAA,0BAARA,QAAQ;EACxB;EADgBA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAaxB;EAbgBA,QAAQ;EAgBxB;EAhBgBA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAyBxB;EAzBgBA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAiCxB;EAjCgBA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EA0CxB;EA1CgBA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAgDxB;EAhDgBA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAARA,QAAQ;EAuExB;EAvEgBA,QAAQ;EAAA,OAARA,QAAQ;AAAA", "ignoreList": []}