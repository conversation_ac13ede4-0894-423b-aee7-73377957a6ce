"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.NodeType = void 0;
let NodeType = exports.NodeType = /*#__PURE__*/function (NodeType) {
  // Shaders
  NodeType["Layer"] = "skLayer";
  NodeType["Shader"] = "skShader";
  NodeType["ImageShader"] = "skImageShader";
  NodeType["ColorShader"] = "skColorShader";
  NodeType["Turbulence"] = "skTurbulence";
  NodeType["FractalNoise"] = "skFractalNoise";
  NodeType["LinearGradient"] = "skLinearGradient";
  NodeType["RadialGradient"] = "skRadialGradient";
  NodeType["SweepGradient"] = "skSweepGradient";
  NodeType["TwoPointConicalGradient"] = "skTwoPointConicalGradient";
  // Mask Filters
  NodeType["BlurMaskFilter"] = "skBlurMaskFilter";
  // Path Effects
  NodeType["DiscretePathEffect"] = "skDiscretePathEffect";
  NodeType["DashPathEffect"] = "skDashPathEffect";
  NodeType["Path1DPathEffect"] = "skPath1DPathEffect";
  NodeType["Path2DPathEffect"] = "skPath2DPathEffect";
  NodeType["CornerPathEffect"] = "skCornerPathEffect";
  NodeType["SumPathEffect"] = "skSumPathEffect";
  NodeType["Line2DPathEffect"] = "skLine2DPathEffect";
  // Color Filters
  NodeType["MatrixColorFilter"] = "skMatrixColorFilter";
  NodeType["BlendColorFilter"] = "skBlendColorFilter";
  NodeType["LinearToSRGBGammaColorFilter"] = "skLinearToSRGBGammaColorFilter";
  NodeType["SRGBToLinearGammaColorFilter"] = "skSRGBToLinearGammaColorFilter";
  NodeType["LumaColorFilter"] = "skLumaColorFilter";
  NodeType["LerpColorFilter"] = "skLerpColorFilter";
  // Image Filters
  NodeType["OffsetImageFilter"] = "skOffsetImageFilter";
  NodeType["DisplacementMapImageFilter"] = "skDisplacementMapImageFilter";
  NodeType["BlurImageFilter"] = "skBlurImageFilter";
  NodeType["DropShadowImageFilter"] = "skDropShadowImageFilter";
  NodeType["MorphologyImageFilter"] = "skMorphologyImageFilter";
  NodeType["BlendImageFilter"] = "skBlendImageFilter";
  NodeType["RuntimeShaderImageFilter"] = "skRuntimeShaderImageFilter";
  // Mixed
  NodeType["Blend"] = "skBlend";
  NodeType["BackdropFilter"] = "skBackdropFilter";
  NodeType["Box"] = "skBox";
  NodeType["BoxShadow"] = "skBoxShadow";
  // Drawings
  NodeType["Group"] = "skGroup";
  NodeType["Paint"] = "skPaint";
  NodeType["Circle"] = "skCircle";
  NodeType["Fill"] = "skFill";
  NodeType["Image"] = "skImage";
  NodeType["Points"] = "skPoints";
  NodeType["Path"] = "skPath";
  NodeType["Rect"] = "skRect";
  NodeType["RRect"] = "skRRect";
  NodeType["Oval"] = "skOval";
  NodeType["Line"] = "skLine";
  NodeType["Patch"] = "skPatch";
  NodeType["Vertices"] = "skVertices";
  NodeType["DiffRect"] = "skDiffRect";
  NodeType["Text"] = "skText";
  NodeType["TextPath"] = "skTextPath";
  NodeType["TextBlob"] = "skTextBlob";
  NodeType["Glyphs"] = "skGlyphs";
  NodeType["Picture"] = "skPicture";
  NodeType["ImageSVG"] = "skImageSVG";
  NodeType["Atlas"] = "skAtlas";
  // Paragraph
  NodeType["Paragraph"] = "skParagraph";
  return NodeType;
}({});
//# sourceMappingURL=NodeType.js.map