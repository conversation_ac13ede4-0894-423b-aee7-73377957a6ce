{"version": 3, "names": [], "sources": ["Node.ts"], "sourcesContent": ["import type { GroupProps } from \"./Common\";\nimport type { NodeType } from \"./NodeType\";\n\nexport interface Node<P> {\n  type: NodeType;\n\n  setProps(props: P): void;\n  setProp<K extends keyof P>(name: K, v: P[K]): boolean;\n  getProps(): P;\n\n  children(): Node<unknown>[];\n  addChild(child: Node<unknown>): void;\n  removeChild(child: Node<unknown>): void;\n  insertChildBefore(child: Node<unknown>, before: Node<unknown>): void;\n}\n\nexport type Invalidate = () => void;\n\nexport interface DeclarationNode<P> extends Node<P> {\n  setInvalidate(invalidate: Invalidate): void;\n}\n\nexport type RenderNode<P extends GroupProps> = Node<P>;\n"], "mappings": "", "ignoreList": []}