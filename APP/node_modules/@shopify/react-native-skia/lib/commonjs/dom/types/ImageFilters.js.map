{"version": 3, "names": [], "sources": ["ImageFilters.ts"], "sourcesContent": ["import type {\n  BlendMode,\n  Color,\n  ColorChannel,\n  SkRuntimeEffect,\n  TileMode,\n  Uniforms,\n} from \"../../skia/types\";\n\nimport type { Radius, SkEnum, ChildrenProps } from \"./Common\";\n\nexport interface BlurImageFilterProps extends ChildrenProps {\n  blur: Radius;\n  mode: SkEnum<typeof TileMode>;\n}\n\nexport interface OffsetImageFilterProps extends ChildrenProps {\n  x: number;\n  y: number;\n}\n\nexport interface RuntimeShaderImageFilterProps extends ChildrenProps {\n  source: SkRuntimeEffect;\n  uniforms?: Uniforms;\n}\n\n// TODO: delete\nexport interface BlendImageFilterProps extends ChildrenProps {\n  mode: SkEnum<typeof BlendMode>;\n}\n\nexport interface MorphologyImageFilterProps extends ChildrenProps {\n  operator: \"erode\" | \"dilate\";\n  radius: Radius;\n}\n\nexport interface DropShadowImageFilterProps extends ChildrenProps {\n  dx: number;\n  dy: number;\n  blur: number;\n  color: Color;\n  inner?: boolean;\n  shadowOnly?: boolean;\n}\n\nexport interface DisplacementMapImageFilterProps extends ChildrenProps {\n  channelX: SkEnum<typeof ColorChannel>;\n  channelY: SkEnum<typeof ColorChannel>;\n  scale: number;\n}\n\nexport interface BlendProps extends ChildrenProps {\n  mode: SkEnum<typeof BlendMode>;\n}\n"], "mappings": "", "ignoreList": []}