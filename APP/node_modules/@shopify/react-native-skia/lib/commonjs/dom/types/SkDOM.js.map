{"version": 3, "names": [], "sources": ["SkDOM.ts"], "sourcesContent": ["import type { ChildrenProps, GroupProps, PaintProps } from \"./Common\";\nimport type {\n  BlendImageFilterProps,\n  BlurImageFilterProps,\n  DropShadowImageFilterProps,\n  OffsetImageFilterProps,\n  RuntimeShaderImageFilterProps,\n  DisplacementMapImageFilterProps,\n  MorphologyImageFilterProps,\n  BlendProps,\n} from \"./ImageFilters\";\nimport type { DeclarationNode, RenderNode } from \"./Node\";\nimport type {\n  BlendColorFilterProps,\n  MatrixColorFilterProps,\n  LerpColorFilterProps,\n} from \"./ColorFilters\";\nimport type {\n  ImageProps,\n  CircleProps,\n  PathProps,\n  LineProps,\n  OvalProps,\n  PatchProps,\n  PointsProps,\n  RectProps,\n  RoundedRectProps,\n  VerticesProps,\n  TextProps,\n  DiffRectProps,\n  TextPathProps,\n  TextBlobProps,\n  GlyphsProps,\n  PictureProps,\n  ImageSVGProps,\n  DrawingNodeProps,\n  BoxProps,\n  BoxShadowProps,\n  AtlasProps,\n} from \"./Drawings\";\nimport type { BlurMaskFilterProps } from \"./MaskFilters\";\nimport type {\n  FractalNoiseProps,\n  SweepGradientProps,\n  ImageShaderProps,\n  LinearGradientProps,\n  ShaderProps,\n  TurbulenceProps,\n  TwoPointConicalGradientProps,\n  RadialGradientProps,\n  ColorProps,\n} from \"./Shaders\";\nimport type {\n  CornerPathEffectProps,\n  DashPathEffectProps,\n  DiscretePathEffectProps,\n  Line2DPathEffectProps,\n  Path1DPathEffectProps,\n  Path2DPathEffectProps,\n} from \"./PathEffects\";\nimport type { ParagraphProps } from \"./Paragraph\";\n\ntype ImageFilterNode<P> = DeclarationNode<P>;\n\ntype PathEffectNode<P> = DeclarationNode<P>;\ntype NullablePathEffectNode<P> = DeclarationNode<P>;\n\ntype DrawingNode<P extends GroupProps> = RenderNode<P>;\n\nexport interface SkDOM {\n  Layer(props?: ChildrenProps): RenderNode<ChildrenProps>;\n  Group(props?: GroupProps): RenderNode<GroupProps>;\n  Paint(props: PaintProps): DeclarationNode<PaintProps>;\n\n  // Drawings\n  Fill(props?: DrawingNodeProps): DrawingNode<DrawingNodeProps>;\n  Image(props: ImageProps): DrawingNode<ImageProps>;\n  Circle(props: CircleProps): DrawingNode<CircleProps>;\n  Path(props: PathProps): DrawingNode<PathProps>;\n  Line(props: LineProps): DrawingNode<LineProps>;\n  Oval(props: OvalProps): DrawingNode<OvalProps>;\n  Patch(props: PatchProps): DrawingNode<PatchProps>;\n  Points(props: PointsProps): DrawingNode<PointsProps>;\n  Rect(props: RectProps): DrawingNode<RectProps>;\n  RRect(props: RoundedRectProps): DrawingNode<RoundedRectProps>;\n  Vertices(props: VerticesProps): DrawingNode<VerticesProps>;\n  Text(props: TextProps): DrawingNode<TextProps>;\n  TextPath(props: TextPathProps): DrawingNode<TextPathProps>;\n  TextBlob(props: TextBlobProps): DrawingNode<TextBlobProps>;\n  Glyphs(props: GlyphsProps): DrawingNode<GlyphsProps>;\n  DiffRect(props: DiffRectProps): DrawingNode<DiffRectProps>;\n  Picture(props: PictureProps): DrawingNode<PictureProps>;\n  ImageSVG(props: ImageSVGProps): DrawingNode<ImageSVGProps>;\n  Atlas(props: AtlasProps): DrawingNode<AtlasProps>;\n\n  // BlurMaskFilters\n  BlurMaskFilter(\n    props: BlurMaskFilterProps\n  ): DeclarationNode<BlurMaskFilterProps>;\n\n  // ImageFilters\n  BlendImageFilter(\n    props: BlendImageFilterProps\n  ): ImageFilterNode<BlendImageFilterProps>;\n  BlurImageFilter(\n    props: BlurImageFilterProps\n  ): ImageFilterNode<BlurImageFilterProps>;\n  OffsetImageFilter(\n    props: OffsetImageFilterProps\n  ): ImageFilterNode<OffsetImageFilterProps>;\n  DropShadowImageFilter(\n    props: DropShadowImageFilterProps\n  ): ImageFilterNode<DropShadowImageFilterProps>;\n  MorphologyImageFilter(\n    props: MorphologyImageFilterProps\n  ): ImageFilterNode<MorphologyImageFilterProps>;\n  DisplacementMapImageFilter(\n    props: DisplacementMapImageFilterProps\n  ): ImageFilterNode<DisplacementMapImageFilterProps>;\n  RuntimeShaderImageFilter(\n    props: RuntimeShaderImageFilterProps\n  ): ImageFilterNode<RuntimeShaderImageFilterProps>;\n\n  // ColorFilters\n  MatrixColorFilter(\n    props: MatrixColorFilterProps\n  ): DeclarationNode<MatrixColorFilterProps>;\n  BlendColorFilter(\n    props: BlendColorFilterProps\n  ): DeclarationNode<BlendColorFilterProps>;\n  LumaColorFilter(): DeclarationNode<null>;\n  LinearToSRGBGammaColorFilter(): DeclarationNode<null>;\n  SRGBToLinearGammaColorFilter(): DeclarationNode<null>;\n  LerpColorFilter(\n    props: LerpColorFilterProps\n  ): DeclarationNode<LerpColorFilterProps>;\n\n  // Shaders\n  Shader(props: ShaderProps): DeclarationNode<ShaderProps>;\n  ImageShader(props: ImageShaderProps): DeclarationNode<ImageShaderProps>;\n  ColorShader(props: ColorProps): DeclarationNode<ColorProps>;\n  Turbulence(props: TurbulenceProps): DeclarationNode<TurbulenceProps>;\n  FractalNoise(props: FractalNoiseProps): DeclarationNode<FractalNoiseProps>;\n  LinearGradient(\n    props: LinearGradientProps\n  ): DeclarationNode<LinearGradientProps>;\n  RadialGradient(\n    props: RadialGradientProps\n  ): DeclarationNode<RadialGradientProps>;\n  SweepGradient(props: SweepGradientProps): DeclarationNode<SweepGradientProps>;\n  TwoPointConicalGradient(\n    props: TwoPointConicalGradientProps\n  ): DeclarationNode<TwoPointConicalGradientProps>;\n\n  // Path Effects\n  CornerPathEffect(\n    props: CornerPathEffectProps\n  ): NullablePathEffectNode<CornerPathEffectProps>;\n  DiscretePathEffect(\n    props: DiscretePathEffectProps\n  ): PathEffectNode<DiscretePathEffectProps>;\n  DashPathEffect(\n    props: DashPathEffectProps\n  ): PathEffectNode<DashPathEffectProps>;\n  Path1DPathEffect(\n    props: Path1DPathEffectProps\n  ): NullablePathEffectNode<Path1DPathEffectProps>;\n  Path2DPathEffect(\n    props: Path2DPathEffectProps\n  ): NullablePathEffectNode<Path2DPathEffectProps>;\n  SumPathEffect(): NullablePathEffectNode<null>;\n  Line2DPathEffect(\n    props: Line2DPathEffectProps\n  ): NullablePathEffectNode<Line2DPathEffectProps>;\n\n  // Mixed\n  Blend(props: BlendProps): DeclarationNode<BlendProps>;\n  BackdropFilter(props: ChildrenProps): RenderNode<ChildrenProps>;\n  Box(props: BoxProps): RenderNode<BoxProps>;\n  BoxShadow(props: BoxShadowProps): DeclarationNode<BoxShadowProps>;\n\n  // Paragraph\n  Paragraph(props: ParagraphProps): RenderNode<ParagraphProps>;\n}\n"], "mappings": "", "ignoreList": []}