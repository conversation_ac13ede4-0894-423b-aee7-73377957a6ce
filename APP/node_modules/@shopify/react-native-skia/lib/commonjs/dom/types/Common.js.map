{"version": 3, "names": [], "sources": ["Common.ts"], "sourcesContent": ["import type { ReactNode } from \"react\";\n\nimport type {\n  BlendMode,\n  Color,\n  InputMatrix,\n  InputRRect,\n  PaintStyle,\n  SkPaint,\n  SkPath,\n  SkRect,\n  SkRRect,\n  StrokeCap,\n  StrokeJoin,\n  Transforms3d,\n  Vector,\n} from \"../../skia/types\";\n\nexport type SkEnum<T> = Uncapitalize<keyof T extends string ? keyof T : never>;\n\nexport type PathDef = string | SkPath;\n\nexport type ClipDef = SkRRect | SkRect | PathDef;\n\nexport type Fit =\n  | \"cover\"\n  | \"contain\"\n  | \"fill\"\n  | \"fitHeight\"\n  | \"fitWidth\"\n  | \"none\"\n  | \"scaleDown\";\n\nexport type Radius = number | Vector;\n\nexport interface ChildrenProps {\n  children?: ReactNode | ReactNode[];\n}\n\nexport interface RectCtor {\n  x?: number;\n  y?: number;\n  width: number;\n  height: number;\n}\n\nexport interface RRectCtor extends RectCtor {\n  r?: Radius;\n}\n\nexport type RectDef = RectCtor | { rect: SkRect };\nexport type RRectDef = RRectCtor | { rect: InputRRect };\n\nexport interface PointCircleDef {\n  c?: Vector;\n  r: number;\n}\n\nexport interface ScalarCircleDef {\n  cx: number;\n  cy: number;\n  r: number;\n}\n\nexport type CircleDef = PointCircleDef | ScalarCircleDef;\n\nexport interface TransformProps {\n  transform?: Transforms3d;\n  origin?: Vector;\n  matrix?: InputMatrix;\n}\n\nexport interface CTMProps extends TransformProps {\n  clip?: ClipDef;\n  invertClip?: boolean;\n  layer?: SkPaint | boolean;\n}\n\nexport interface PaintProps extends ChildrenProps {\n  color?: Color;\n  strokeWidth?: number;\n  blendMode?: SkEnum<typeof BlendMode>;\n  style?: SkEnum<typeof PaintStyle>;\n  strokeJoin?: SkEnum<typeof StrokeJoin>;\n  strokeCap?: SkEnum<typeof StrokeCap>;\n  strokeMiter?: number;\n  opacity?: number;\n  antiAlias?: boolean;\n  dither?: boolean;\n}\n\nexport interface GroupProps extends PaintProps, CTMProps {}\n"], "mappings": "", "ignoreList": []}