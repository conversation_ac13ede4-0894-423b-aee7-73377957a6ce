export declare const enum NodeType {
    Layer = "skLayer",
    Shader = "skShader",
    ImageShader = "skImageShader",
    ColorShader = "skColorShader",
    Turbulence = "skTurbulence",
    FractalNoise = "skFractal<PERSON><PERSON>",
    LinearGradient = "skLinearGradient",
    RadialGradient = "skRadialGradient",
    SweepGradient = "skSweepGradient",
    TwoPointConicalGradient = "skTwoPointConicalGradient",
    BlurMaskFilter = "skBlurMaskFilter",
    DiscretePathEffect = "skDiscretePathEffect",
    DashPathEffect = "skDashPathEffect",
    Path1DPathEffect = "skPath1DPathEffect",
    Path2DPathEffect = "skPath2DPathEffect",
    CornerPathEffect = "skCornerPathEffect",
    SumPathEffect = "skSumPathEffect",
    Line2DPathEffect = "skLine2DPathEffect",
    MatrixColorFilter = "skMatrixColorFilter",
    BlendColorFilter = "skBlendColorFilter",
    LinearToSRGBGammaColorFilter = "skLinearToSRGBGammaColorFilter",
    SRGBToLinearGammaColorFilter = "skSRGBToLinearGammaColorFilter",
    LumaColorFilter = "skLumaColorFilter",
    LerpColorFilter = "skLerpColorFilter",
    OffsetImageFilter = "skOffsetImageFilter",
    DisplacementMapImageFilter = "skDisplacementMapImageFilter",
    BlurImageFilter = "skBlurImageFilter",
    DropShadowImageFilter = "skDropShadowImageFilter",
    MorphologyImageFilter = "skMorphologyImageFilter",
    BlendImageFilter = "skBlendImageFilter",
    RuntimeShaderImageFilter = "skRuntimeShaderImageFilter",
    Blend = "skBlend",
    BackdropFilter = "skBackdropFilter",
    Box = "skBox",
    BoxShadow = "skBoxShadow",
    Group = "skGroup",
    Paint = "skPaint",
    Circle = "skCircle",
    Fill = "skFill",
    Image = "skImage",
    Points = "skPoints",
    Path = "skPath",
    Rect = "skRect",
    RRect = "skRRect",
    Oval = "skOval",
    Line = "skLine",
    Patch = "skPatch",
    Vertices = "skVertices",
    DiffRect = "skDiffRect",
    Text = "skText",
    TextPath = "skTextPath",
    TextBlob = "skTextBlob",
    Glyphs = "skGlyphs",
    Picture = "skPicture",
    ImageSVG = "skImageSVG",
    Atlas = "skAtlas",
    Paragraph = "skParagraph"
}
