{"version": 3, "names": [], "sources": ["Shaders.ts"], "sourcesContent": ["import type {\n  Color,\n  SamplingOptions,\n  SkImage,\n  SkR<PERSON>t,\n  SkRuntimeEffect,\n  TileMode,\n  Uniforms,\n  Vector,\n} from \"../../skia/types\";\n\nimport type {\n  SkEnum,\n  TransformProps,\n  ChildrenProps,\n  RectCtor,\n  Fit,\n} from \"./Common\";\n\nexport interface ShaderProps extends TransformProps, ChildrenProps {\n  source: SkRuntimeEffect;\n  uniforms: Uniforms;\n}\n\nexport interface ImageShaderProps extends TransformProps, Partial<RectCtor> {\n  tx: SkEnum<typeof TileMode>;\n  ty: SkEnum<typeof TileMode>;\n  fit: Fit;\n  rect?: SkRect;\n  image: SkImage | null;\n  sampling?: SamplingOptions;\n}\n\nexport interface ColorProps {\n  color: Color;\n}\n\nexport interface TurbulenceProps {\n  freqX: number;\n  freqY: number;\n  octaves: number;\n  seed: number;\n  tileWidth: number;\n  tileHeight: number;\n}\n\nexport interface FractalNoiseProps {\n  freqX: number;\n  freqY: number;\n  octaves: number;\n  seed: number;\n  tileWidth: number;\n  tileHeight: number;\n}\n\nexport interface GradientProps extends TransformProps {\n  colors: Color[];\n  positions?: number[];\n  mode?: SkEnum<typeof TileMode>;\n  flags?: number;\n}\n\nexport interface LinearGradientProps extends GradientProps {\n  start: Vector;\n  end: Vector;\n}\n\nexport interface RadialGradientProps extends GradientProps {\n  c: Vector;\n  r: number;\n}\n\nexport interface SweepGradientProps extends GradientProps {\n  c: Vector;\n  start?: number;\n  end?: number;\n}\n\nexport interface TwoPointConicalGradientProps extends GradientProps {\n  start: Vector;\n  startR: number;\n  end: Vector;\n  endR: number;\n}\n"], "mappings": "", "ignoreList": []}