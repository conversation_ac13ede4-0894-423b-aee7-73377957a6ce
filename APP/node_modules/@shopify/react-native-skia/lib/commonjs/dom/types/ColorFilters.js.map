{"version": 3, "names": [], "sources": ["ColorFilters.ts"], "sourcesContent": ["import type { BlendMode, Color } from \"../../skia/types\";\n\nimport type { ChildrenProps, SkEnum } from \"./Common\";\n\nexport interface MatrixColorFilterProps extends ChildrenProps {\n  matrix: number[];\n}\n\nexport interface BlendColorFilterProps extends ChildrenProps {\n  mode: SkEnum<typeof BlendMode>;\n  color: Color;\n}\n\nexport interface LerpColorFilterProps extends ChildrenProps {\n  t: number;\n}\n"], "mappings": "", "ignoreList": []}