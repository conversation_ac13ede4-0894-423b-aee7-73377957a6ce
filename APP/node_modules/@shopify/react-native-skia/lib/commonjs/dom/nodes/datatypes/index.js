"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
var _Enum = require("./Enum");
Object.keys(_Enum).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Enum[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Enum[key];
    }
  });
});
var _Path = require("./Path");
Object.keys(_Path).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Path[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Path[key];
    }
  });
});
var _Fitting = require("./Fitting");
Object.keys(_Fitting).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Fitting[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Fitting[key];
    }
  });
});
var _Rect = require("./Rect");
Object.keys(_Rect).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Rect[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Rect[key];
    }
  });
});
var _Radius = require("./Radius");
Object.keys(_Radius).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Radius[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Radius[key];
    }
  });
});
var _Circle = require("./Circle");
Object.keys(_Circle).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Circle[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Circle[key];
    }
  });
});
var _Gradient = require("./Gradient");
Object.keys(_Gradient).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Gradient[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Gradient[key];
    }
  });
});
var _Transform = require("./Transform");
Object.keys(_Transform).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  if (key in exports && exports[key] === _Transform[key]) return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return _Transform[key];
    }
  });
});
//# sourceMappingURL=index.js.map