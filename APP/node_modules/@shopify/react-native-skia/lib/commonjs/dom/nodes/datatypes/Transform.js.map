{"version": 3, "names": ["_types", "require", "processTransformProps", "m3", "props", "transform", "origin", "matrix", "translate", "x", "y", "concat", "processTransform", "exports", "processTransformProps2", "Skia", "Matrix"], "sources": ["Transform.ts"], "sourcesContent": ["import type { TransformProps } from \"../../types\";\nimport type { Skia, SkMatrix } from \"../../../skia/types\";\nimport { processTransform } from \"../../../skia/types\";\n\nexport const processTransformProps = (m3: SkMatrix, props: TransformProps) => {\n  \"worklet\";\n\n  const { transform, origin, matrix } = props;\n  if (matrix) {\n    if (origin) {\n      m3.translate(origin.x, origin.y);\n      m3.concat(matrix);\n      m3.translate(-origin.x, -origin.y);\n    } else {\n      m3.concat(matrix);\n    }\n  } else if (transform) {\n    if (origin) {\n      m3.translate(origin.x, origin.y);\n    }\n    processTransform(m3, transform);\n    if (origin) {\n      m3.translate(-origin.x, -origin.y);\n    }\n  }\n};\n\nexport const processTransformProps2 = (Skia: Skia, props: TransformProps) => {\n  \"worklet\";\n\n  const { transform, origin, matrix } = props;\n  if (matrix) {\n    const m3 = Skia.Matrix();\n    if (origin) {\n      m3.translate(origin.x, origin.y);\n      m3.concat(matrix);\n      m3.translate(-origin.x, -origin.y);\n    } else {\n      m3.concat(matrix);\n    }\n    return m3;\n  } else if (transform) {\n    const m3 = Skia.Matrix();\n    if (origin) {\n      m3.translate(origin.x, origin.y);\n    }\n    processTransform(m3, transform);\n    if (origin) {\n      m3.translate(-origin.x, -origin.y);\n    }\n    return m3;\n  }\n  return null;\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,MAAA,GAAAC,OAAA;AAEO,MAAMC,qBAAqB,GAAGA,CAACC,EAAY,EAAEC,KAAqB,KAAK;EAC5E,SAAS;;EAET,MAAM;IAAEC,SAAS;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGH,KAAK;EAC3C,IAAIG,MAAM,EAAE;IACV,IAAID,MAAM,EAAE;MACVH,EAAE,CAACK,SAAS,CAACF,MAAM,CAACG,CAAC,EAAEH,MAAM,CAACI,CAAC,CAAC;MAChCP,EAAE,CAACQ,MAAM,CAACJ,MAAM,CAAC;MACjBJ,EAAE,CAACK,SAAS,CAAC,CAACF,MAAM,CAACG,CAAC,EAAE,CAACH,MAAM,CAACI,CAAC,CAAC;IACpC,CAAC,MAAM;MACLP,EAAE,CAACQ,MAAM,CAACJ,MAAM,CAAC;IACnB;EACF,CAAC,MAAM,IAAIF,SAAS,EAAE;IACpB,IAAIC,MAAM,EAAE;MACVH,EAAE,CAACK,SAAS,CAACF,MAAM,CAACG,CAAC,EAAEH,MAAM,CAACI,CAAC,CAAC;IAClC;IACA,IAAAE,uBAAgB,EAACT,EAAE,EAAEE,SAAS,CAAC;IAC/B,IAAIC,MAAM,EAAE;MACVH,EAAE,CAACK,SAAS,CAAC,CAACF,MAAM,CAACG,CAAC,EAAE,CAACH,MAAM,CAACI,CAAC,CAAC;IACpC;EACF;AACF,CAAC;AAACG,OAAA,CAAAX,qBAAA,GAAAA,qBAAA;AAEK,MAAMY,sBAAsB,GAAGA,CAACC,IAAU,EAAEX,KAAqB,KAAK;EAC3E,SAAS;;EAET,MAAM;IAAEC,SAAS;IAAEC,MAAM;IAAEC;EAAO,CAAC,GAAGH,KAAK;EAC3C,IAAIG,MAAM,EAAE;IACV,MAAMJ,EAAE,GAAGY,IAAI,CAACC,MAAM,CAAC,CAAC;IACxB,IAAIV,MAAM,EAAE;MACVH,EAAE,CAACK,SAAS,CAACF,MAAM,CAACG,CAAC,EAAEH,MAAM,CAACI,CAAC,CAAC;MAChCP,EAAE,CAACQ,MAAM,CAACJ,MAAM,CAAC;MACjBJ,EAAE,CAACK,SAAS,CAAC,CAACF,MAAM,CAACG,CAAC,EAAE,CAACH,MAAM,CAACI,CAAC,CAAC;IACpC,CAAC,MAAM;MACLP,EAAE,CAACQ,MAAM,CAACJ,MAAM,CAAC;IACnB;IACA,OAAOJ,EAAE;EACX,CAAC,MAAM,IAAIE,SAAS,EAAE;IACpB,MAAMF,EAAE,GAAGY,IAAI,CAACC,MAAM,CAAC,CAAC;IACxB,IAAIV,MAAM,EAAE;MACVH,EAAE,CAACK,SAAS,CAACF,MAAM,CAACG,CAAC,EAAEH,MAAM,CAACI,CAAC,CAAC;IAClC;IACA,IAAAE,uBAAgB,EAACT,EAAE,EAAEE,SAAS,CAAC;IAC/B,IAAIC,MAAM,EAAE;MACVH,EAAE,CAACK,SAAS,CAAC,CAACF,MAAM,CAACG,CAAC,EAAE,CAACH,MAAM,CAACI,CAAC,CAAC;IACpC;IACA,OAAOP,EAAE;EACX;EACA,OAAO,IAAI;AACb,CAAC;AAACU,OAAA,CAAAC,sBAAA,GAAAA,sBAAA", "ignoreList": []}