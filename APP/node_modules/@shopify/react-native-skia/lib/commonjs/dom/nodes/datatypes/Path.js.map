{"version": 3, "names": ["_types", "require", "processPath", "Skia", "rawPath", "path", "Path", "MakeFromSVGString", "Error", "exports", "isPathDef", "def", "isPath"], "sources": ["Path.ts"], "sourcesContent": ["import type { Skia } from \"../../../skia/types\";\nimport { isPath } from \"../../../skia/types\";\nimport type { PathDef } from \"../../types\";\n\nexport const processPath = (Skia: Skia, rawPath: PathDef) => {\n  \"worklet\";\n  const path =\n    typeof rawPath === \"string\"\n      ? Skia.Path.MakeFromSVGString(rawPath)\n      : rawPath;\n  if (!path) {\n    throw new Error(\"Invalid path: \" + rawPath);\n  }\n  return path;\n};\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport const isPathDef = (def: any): def is PathDef => {\n  \"worklet\";\n  return typeof def === \"string\" || isPath(def);\n};\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAGO,MAAMC,WAAW,GAAGA,CAACC,IAAU,EAAEC,OAAgB,KAAK;EAC3D,SAAS;;EACT,MAAMC,IAAI,GACR,OAAOD,OAAO,KAAK,QAAQ,GACvBD,IAAI,CAACG,IAAI,CAACC,iBAAiB,CAACH,OAAO,CAAC,GACpCA,OAAO;EACb,IAAI,CAACC,IAAI,EAAE;IACT,MAAM,IAAIG,KAAK,CAAC,gBAAgB,GAAGJ,OAAO,CAAC;EAC7C;EACA,OAAOC,IAAI;AACb,CAAC;;AAED;AAAAI,OAAA,CAAAP,WAAA,GAAAA,WAAA;AACO,MAAMQ,SAAS,GAAIC,GAAQ,IAAqB;EACrD,SAAS;;EACT,OAAO,OAAOA,GAAG,KAAK,QAAQ,IAAI,IAAAC,aAAM,EAACD,GAAG,CAAC;AAC/C,CAAC;AAACF,OAAA,CAAAC,SAAA,GAAAA,SAAA", "ignoreList": []}