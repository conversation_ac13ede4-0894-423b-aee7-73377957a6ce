{"version": 3, "names": ["_Radius", "require", "isEdge", "pos", "b", "x", "y", "width", "height", "exports", "isRRectCtor", "def", "rect", "undefined", "isRectCtor", "processRect", "Skia", "_def$x", "_def$y", "XYWHRect", "processRRect", "_def$r", "_def$x2", "_def$y2", "r", "processRadius", "RRectXY", "inflate", "box", "dx", "dy", "tx", "ty", "rx", "ry", "deflate"], "sources": ["Rect.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { <PERSON><PERSON>, SkR<PERSON>t, SkRRect, Vector } from \"../../../skia/types\";\nimport type { RectCtor, RectDef, RRectCtor, RRectDef } from \"../../types\";\n\nimport { processRadius } from \"./Radius\";\n\nexport const isEdge = (pos: Vector, b: SkRect) => {\n  \"worklet\";\n  return (\n    pos.x === b.x || pos.y === b.y || pos.x === b.width || pos.y === b.height\n  );\n};\n\n// We have an issue to check property existence on JSI backed instances\nconst isRRectCtor = (def: RRectDef): def is RRectCtor => {\n  \"worklet\";\n  return (def as any).rect === undefined;\n};\n// We have an issue to check property existence on JSI backed instances\nconst isRectCtor = (def: RectDef): def is RectCtor => {\n  \"worklet\";\n  return (def as any).rect === undefined;\n};\n\nexport const processRect = (Skia: Skia, def: RectDef) => {\n  \"worklet\";\n  if (isRectCtor(def)) {\n    return Skia.XYWHRect(def.x ?? 0, def.y ?? 0, def.width, def.height);\n  } else {\n    return def.rect;\n  }\n};\n\nexport const processRRect = (Skia: Skia, def: RRectDef) => {\n  \"worklet\";\n  if (isRRectCtor(def)) {\n    const r = processRadius(Skia, def.r ?? 0);\n    return Skia.RRectXY(\n      Skia.XYWHRect(def.x ?? 0, def.y ?? 0, def.width, def.height),\n      r.x,\n      r.y\n    );\n  } else {\n    return def.rect;\n  }\n};\n\nexport const inflate = (\n  Skia: Skia,\n  box: SkRRect,\n  dx: number,\n  dy: number,\n  tx = 0,\n  ty = 0\n) => {\n  \"worklet\";\n  return Skia.RRectXY(\n    Skia.XYWHRect(\n      box.rect.x - dx + tx,\n      box.rect.y - dy + ty,\n      box.rect.width + 2 * dx,\n      box.rect.height + 2 * dy\n    ),\n    box.rx + dx,\n    box.ry + dy\n  );\n};\n\nexport const deflate = (\n  Skia: Skia,\n  box: SkRRect,\n  dx: number,\n  dy: number,\n  tx = 0,\n  ty = 0\n) => {\n  \"worklet\";\n  return inflate(Skia, box, -dx, -dy, tx, ty);\n};\n"], "mappings": ";;;;;;AAIA,IAAAA,OAAA,GAAAC,OAAA;AAJA;;AAMO,MAAMC,MAAM,GAAGA,CAACC,GAAW,EAAEC,CAAS,KAAK;EAChD,SAAS;;EACT,OACED,GAAG,CAACE,CAAC,KAAKD,CAAC,CAACC,CAAC,IAAIF,GAAG,CAACG,CAAC,KAAKF,CAAC,CAACE,CAAC,IAAIH,GAAG,CAACE,CAAC,KAAKD,CAAC,CAACG,KAAK,IAAIJ,GAAG,CAACG,CAAC,KAAKF,CAAC,CAACI,MAAM;AAE7E,CAAC;;AAED;AAAAC,OAAA,CAAAP,MAAA,GAAAA,MAAA;AACA,MAAMQ,WAAW,GAAIC,GAAa,IAAuB;EACvD,SAAS;;EACT,OAAQA,GAAG,CAASC,IAAI,KAAKC,SAAS;AACxC,CAAC;AACD;AACA,MAAMC,UAAU,GAAIH,GAAY,IAAsB;EACpD,SAAS;;EACT,OAAQA,GAAG,CAASC,IAAI,KAAKC,SAAS;AACxC,CAAC;AAEM,MAAME,WAAW,GAAGA,CAACC,IAAU,EAAEL,GAAY,KAAK;EACvD,SAAS;;EACT,IAAIG,UAAU,CAACH,GAAG,CAAC,EAAE;IAAA,IAAAM,MAAA,EAAAC,MAAA;IACnB,OAAOF,IAAI,CAACG,QAAQ,EAAAF,MAAA,GAACN,GAAG,CAACN,CAAC,cAAAY,MAAA,cAAAA,MAAA,GAAI,CAAC,GAAAC,MAAA,GAAEP,GAAG,CAACL,CAAC,cAAAY,MAAA,cAAAA,MAAA,GAAI,CAAC,EAAEP,GAAG,CAACJ,KAAK,EAAEI,GAAG,CAACH,MAAM,CAAC;EACrE,CAAC,MAAM;IACL,OAAOG,GAAG,CAACC,IAAI;EACjB;AACF,CAAC;AAACH,OAAA,CAAAM,WAAA,GAAAA,WAAA;AAEK,MAAMK,YAAY,GAAGA,CAACJ,IAAU,EAAEL,GAAa,KAAK;EACzD,SAAS;;EACT,IAAID,WAAW,CAACC,GAAG,CAAC,EAAE;IAAA,IAAAU,MAAA,EAAAC,OAAA,EAAAC,OAAA;IACpB,MAAMC,CAAC,GAAG,IAAAC,qBAAa,EAACT,IAAI,GAAAK,MAAA,GAAEV,GAAG,CAACa,CAAC,cAAAH,MAAA,cAAAA,MAAA,GAAI,CAAC,CAAC;IACzC,OAAOL,IAAI,CAACU,OAAO,CACjBV,IAAI,CAACG,QAAQ,EAAAG,OAAA,GAACX,GAAG,CAACN,CAAC,cAAAiB,OAAA,cAAAA,OAAA,GAAI,CAAC,GAAAC,OAAA,GAAEZ,GAAG,CAACL,CAAC,cAAAiB,OAAA,cAAAA,OAAA,GAAI,CAAC,EAAEZ,GAAG,CAACJ,KAAK,EAAEI,GAAG,CAACH,MAAM,CAAC,EAC5DgB,CAAC,CAACnB,CAAC,EACHmB,CAAC,CAAClB,CACJ,CAAC;EACH,CAAC,MAAM;IACL,OAAOK,GAAG,CAACC,IAAI;EACjB;AACF,CAAC;AAACH,OAAA,CAAAW,YAAA,GAAAA,YAAA;AAEK,MAAMO,OAAO,GAAGA,CACrBX,IAAU,EACVY,GAAY,EACZC,EAAU,EACVC,EAAU,EACVC,EAAE,GAAG,CAAC,EACNC,EAAE,GAAG,CAAC,KACH;EACH,SAAS;;EACT,OAAOhB,IAAI,CAACU,OAAO,CACjBV,IAAI,CAACG,QAAQ,CACXS,GAAG,CAAChB,IAAI,CAACP,CAAC,GAAGwB,EAAE,GAAGE,EAAE,EACpBH,GAAG,CAAChB,IAAI,CAACN,CAAC,GAAGwB,EAAE,GAAGE,EAAE,EACpBJ,GAAG,CAAChB,IAAI,CAACL,KAAK,GAAG,CAAC,GAAGsB,EAAE,EACvBD,GAAG,CAAChB,IAAI,CAACJ,MAAM,GAAG,CAAC,GAAGsB,EACxB,CAAC,EACDF,GAAG,CAACK,EAAE,GAAGJ,EAAE,EACXD,GAAG,CAACM,EAAE,GAAGJ,EACX,CAAC;AACH,CAAC;AAACrB,OAAA,CAAAkB,OAAA,GAAAA,OAAA;AAEK,MAAMQ,OAAO,GAAGA,CACrBnB,IAAU,EACVY,GAAY,EACZC,EAAU,EACVC,EAAU,EACVC,EAAE,GAAG,CAAC,EACNC,EAAE,GAAG,CAAC,KACH;EACH,SAAS;;EACT,OAAOL,OAAO,CAACX,IAAI,EAAEY,GAAG,EAAE,CAACC,EAAE,EAAE,CAACC,EAAE,EAAEC,EAAE,EAAEC,EAAE,CAAC;AAC7C,CAAC;AAACvB,OAAA,CAAA0B,OAAA,GAAAA,OAAA", "ignoreList": []}