{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "k", "char<PERSON>t", "toUpperCase", "slice", "exports"], "sources": ["Enum.ts"], "sourcesContent": ["export const enumKey = <K extends string>(k: K) => {\n  \"worklet\";\n  return (k.charAt(0).toUpperCase() + k.slice(1)) as Capitalize<K>;\n};\n"], "mappings": ";;;;;;AAAO,MAAMA,OAAO,GAAsBC,CAAI,IAAK;EACjD,SAAS;;EACT,OAAQA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,CAAC,CAACG,KAAK,CAAC,CAAC,CAAC;AAChD,CAAC;AAACC,OAAA,CAAAL,OAAA,GAAAA,OAAA", "ignoreList": []}