{"version": 3, "names": ["processRadius", "Skia", "radius", "Point", "exports"], "sources": ["Radius.ts"], "sourcesContent": ["import type { <PERSON><PERSON>, Vector } from \"../../../skia/types\";\nimport type { Radius } from \"../../types\";\n\nexport const processRadius = (Skia: Skia, radius: Radius): Vector => {\n  \"worklet\";\n  if (typeof radius === \"number\") {\n    return Skia.Point(radius, radius);\n  }\n  return radius;\n};\n"], "mappings": ";;;;;;AAGO,MAAMA,aAAa,GAAGA,CAACC,IAAU,EAAEC,MAAc,KAAa;EACnE,SAAS;;EACT,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAOD,IAAI,CAACE,KAAK,CAACD,MAAM,EAAEA,MAAM,CAAC;EACnC;EACA,OAAOA,MAAM;AACf,CAAC;AAACE,OAAA,CAAAJ,aAAA,GAAAA,aAAA", "ignoreList": []}