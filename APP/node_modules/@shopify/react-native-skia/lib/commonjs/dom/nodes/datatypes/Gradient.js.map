{"version": 3, "names": ["_types", "require", "_Enum", "_Transform", "transform<PERSON><PERSON>in", "origin", "transform", "translateX", "x", "translateY", "y", "exports", "processColor", "Skia", "color", "Color", "Array", "isArray", "Float32Array", "Error", "processGradientProps", "colors", "positions", "mode", "flags", "localMatrix", "Matrix", "processTransformProps", "map", "TileMode", "<PERSON><PERSON><PERSON><PERSON>", "getRect", "props", "width", "height", "rect", "undefined", "XYWHRect"], "sources": ["Gradient.ts"], "sourcesContent": ["import type { Ski<PERSON>, SkRect, Transforms3d, Vector } from \"../../../skia/types\";\nimport { TileMode } from \"../../../skia/types\";\nimport type { GradientProps, ImageShaderProps } from \"../../types\";\n\nimport { enumKey } from \"./Enum\";\nimport { processTransformProps } from \"./Transform\";\n\nexport const transformOrigin = (origin: Vector, transform: Transforms3d) => {\n  \"worklet\";\n  return [\n    { translateX: origin.x },\n    { translateY: origin.y },\n    ...transform,\n    { translateX: -origin.x },\n    { translateY: -origin.y },\n  ];\n};\n\nexport const processColor = (\n  Skia: Skia,\n  color: number | string | Float32Array | number[]\n) => {\n  \"worklet\";\n  if (typeof color === \"string\" || typeof color === \"number\") {\n    return Skia.Color(color);\n  } else if (Array.isArray(color) || color instanceof Float32Array) {\n    return color instanceof Float32Array ? color : new Float32Array(color);\n  } else {\n    throw new Error(\n      `Invalid color type: ${typeof color}. Expected number, string, or array.`\n    );\n  }\n};\n\nexport const processGradientProps = (\n  Skia: Skia,\n  { colors, positions, mode, flags, ...transform }: GradientProps\n) => {\n  \"worklet\";\n  const localMatrix = Skia.Matrix();\n  processTransformProps(localMatrix, transform);\n  return {\n    colors: colors.map((color) => processColor(Skia, color)),\n    positions: positions ?? null,\n    mode: TileMode[enumKey(mode ?? \"clamp\")],\n    flags,\n    localMatrix,\n  };\n};\n\nexport const getRect = (\n  Skia: Skia,\n  props: Omit<ImageShaderProps, \"tx\" | \"ty\" | \"fm\" | \"mm\" | \"fit\" | \"image\">\n): SkRect | undefined => {\n  \"worklet\";\n  const { x, y, width, height } = props;\n  if (props.rect) {\n    return props.rect;\n  } else if (width !== undefined && height !== undefined) {\n    return Skia.XYWHRect(x ?? 0, y ?? 0, width, height);\n  } else {\n    return undefined;\n  }\n};\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AAEO,MAAMG,eAAe,GAAGA,CAACC,MAAc,EAAEC,SAAuB,KAAK;EAC1E,SAAS;;EACT,OAAO,CACL;IAAEC,UAAU,EAAEF,MAAM,CAACG;EAAE,CAAC,EACxB;IAAEC,UAAU,EAAEJ,MAAM,CAACK;EAAE,CAAC,EACxB,GAAGJ,SAAS,EACZ;IAAEC,UAAU,EAAE,CAACF,MAAM,CAACG;EAAE,CAAC,EACzB;IAAEC,UAAU,EAAE,CAACJ,MAAM,CAACK;EAAE,CAAC,CAC1B;AACH,CAAC;AAACC,OAAA,CAAAP,eAAA,GAAAA,eAAA;AAEK,MAAMQ,YAAY,GAAGA,CAC1BC,IAAU,EACVC,KAAgD,KAC7C;EACH,SAAS;;EACT,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC1D,OAAOD,IAAI,CAACE,KAAK,CAACD,KAAK,CAAC;EAC1B,CAAC,MAAM,IAAIE,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,IAAIA,KAAK,YAAYI,YAAY,EAAE;IAChE,OAAOJ,KAAK,YAAYI,YAAY,GAAGJ,KAAK,GAAG,IAAII,YAAY,CAACJ,KAAK,CAAC;EACxE,CAAC,MAAM;IACL,MAAM,IAAIK,KAAK,CACb,uBAAuB,OAAOL,KAAK,sCACrC,CAAC;EACH;AACF,CAAC;AAACH,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAEK,MAAMQ,oBAAoB,GAAGA,CAClCP,IAAU,EACV;EAAEQ,MAAM;EAAEC,SAAS;EAAEC,IAAI;EAAEC,KAAK;EAAE,GAAGlB;AAAyB,CAAC,KAC5D;EACH,SAAS;;EACT,MAAMmB,WAAW,GAAGZ,IAAI,CAACa,MAAM,CAAC,CAAC;EACjC,IAAAC,gCAAqB,EAACF,WAAW,EAAEnB,SAAS,CAAC;EAC7C,OAAO;IACLe,MAAM,EAAEA,MAAM,CAACO,GAAG,CAAEd,KAAK,IAAKF,YAAY,CAACC,IAAI,EAAEC,KAAK,CAAC,CAAC;IACxDQ,SAAS,EAAEA,SAAS,aAATA,SAAS,cAATA,SAAS,GAAI,IAAI;IAC5BC,IAAI,EAAEM,eAAQ,CAAC,IAAAC,aAAO,EAACP,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,OAAO,CAAC,CAAC;IACxCC,KAAK;IACLC;EACF,CAAC;AACH,CAAC;AAACd,OAAA,CAAAS,oBAAA,GAAAA,oBAAA;AAEK,MAAMW,OAAO,GAAGA,CACrBlB,IAAU,EACVmB,KAA0E,KACnD;EACvB,SAAS;;EACT,MAAM;IAAExB,CAAC;IAAEE,CAAC;IAAEuB,KAAK;IAAEC;EAAO,CAAC,GAAGF,KAAK;EACrC,IAAIA,KAAK,CAACG,IAAI,EAAE;IACd,OAAOH,KAAK,CAACG,IAAI;EACnB,CAAC,MAAM,IAAIF,KAAK,KAAKG,SAAS,IAAIF,MAAM,KAAKE,SAAS,EAAE;IACtD,OAAOvB,IAAI,CAACwB,QAAQ,CAAC7B,CAAC,aAADA,CAAC,cAADA,CAAC,GAAI,CAAC,EAAEE,CAAC,aAADA,CAAC,cAADA,CAAC,GAAI,CAAC,EAAEuB,KAAK,EAAEC,MAAM,CAAC;EACrD,CAAC,MAAM;IACL,OAAOE,SAAS;EAClB;AACF,CAAC;AAACzB,OAAA,CAAAoB,OAAA,GAAAA,OAAA", "ignoreList": []}