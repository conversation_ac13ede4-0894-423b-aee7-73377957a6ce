{"version": 3, "names": ["isCircleScalarDef", "def", "cx", "undefined", "exports", "processCircle", "_def$c", "c", "x", "y", "cy", "r"], "sources": ["Circle.ts"], "sourcesContent": ["import type { CircleDef, ScalarCircleDef } from \"../../types\";\n\nexport const isCircleScalarDef = (def: CircleDef): def is ScalarCircleDef => {\n  \"worklet\";\n  // We have an issue to check property existence on JSI backed instances\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return (def as any).cx !== undefined;\n};\n\nexport const processCircle = (def: CircleDef) => {\n  \"worklet\";\n  if (isCircleScalarDef(def)) {\n    return { c: { x: def.cx, y: def.cy }, r: def.r };\n  }\n  return { ...def, c: def.c ?? { x: 0, y: 0 } };\n};\n"], "mappings": ";;;;;;AAEO,MAAMA,iBAAiB,GAAIC,GAAc,IAA6B;EAC3E,SAAS;;EACT;EACA;EACA,OAAQA,GAAG,CAASC,EAAE,KAAKC,SAAS;AACtC,CAAC;AAACC,OAAA,CAAAJ,iBAAA,GAAAA,iBAAA;AAEK,MAAMK,aAAa,GAAIJ,GAAc,IAAK;EAC/C,SAAS;;EAAC,IAAAK,MAAA;EACV,IAAIN,iBAAiB,CAACC,GAAG,CAAC,EAAE;IAC1B,OAAO;MAAEM,CAAC,EAAE;QAAEC,CAAC,EAAEP,GAAG,CAACC,EAAE;QAAEO,CAAC,EAAER,GAAG,CAACS;MAAG,CAAC;MAAEC,CAAC,EAAEV,GAAG,CAACU;IAAE,CAAC;EAClD;EACA,OAAO;IAAE,GAAGV,GAAG;IAAEM,CAAC,GAAAD,MAAA,GAAEL,GAAG,CAACM,CAAC,cAAAD,MAAA,cAAAA,MAAA,GAAI;MAAEE,CAAC,EAAE,CAAC;MAAEC,CAAC,EAAE;IAAE;EAAE,CAAC;AAC/C,CAAC;AAACL,OAAA,CAAAC,aAAA,GAAAA,aAAA", "ignoreList": []}