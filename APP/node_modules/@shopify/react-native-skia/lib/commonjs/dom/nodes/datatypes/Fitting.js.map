{"version": 3, "names": ["_typeddash", "require", "size", "width", "height", "exports", "rect2rect", "src", "dst", "scaleX", "scaleY", "translateX", "x", "translateY", "y", "inscribe", "rect", "halfWidthDel<PERSON>", "halfHeightDelta", "applyBoxFit", "fit", "input", "output", "Math", "min", "aspectRatio", "exhaustiveCheck", "fitRects", "sizes"], "sources": ["Fitting.ts"], "sourcesContent": ["import { exhaustiveCheck } from \"../../../renderer/typeddash\";\nimport type { SkRect } from \"../../../skia/types\";\nimport type { Fit } from \"../../types\";\n\nexport interface Size {\n  width: number;\n  height: number;\n}\n\nexport const size = (width = 0, height = 0) => {\n  \"worklet\";\n  return { width, height };\n};\n\nexport const rect2rect = (\n  src: SkRect,\n  dst: SkRect\n): [\n  { translateX: number },\n  { translateY: number },\n  { scaleX: number },\n  { scaleY: number }\n] => {\n  \"worklet\";\n  const scaleX = dst.width / src.width;\n  const scaleY = dst.height / src.height;\n  const translateX = dst.x - src.x * scaleX;\n  const translateY = dst.y - src.y * scaleY;\n  return [{ translateX }, { translateY }, { scaleX }, { scaleY }];\n};\n\nconst inscribe = (\n  { width, height }: Size,\n  rect: { x: number; y: number; width: number; height: number }\n) => {\n  \"worklet\";\n  const halfWidthDelta = (rect.width - width) / 2.0;\n  const halfHeightDelta = (rect.height - height) / 2.0;\n  return {\n    x: rect.x + halfWidthDelta,\n    y: rect.y + halfHeightDelta,\n    width,\n    height,\n  };\n};\n\nconst applyBoxFit = (fit: Fit, input: Size, output: Size) => {\n  \"worklet\";\n  let src = size(),\n    dst = size();\n  if (\n    input.height <= 0.0 ||\n    input.width <= 0.0 ||\n    output.height <= 0.0 ||\n    output.width <= 0.0\n  ) {\n    return { src, dst };\n  }\n  switch (fit) {\n    case \"fill\":\n      src = input;\n      dst = output;\n      break;\n    case \"contain\":\n      src = input;\n      if (output.width / output.height > src.width / src.height) {\n        dst = size((src.width * output.height) / src.height, output.height);\n      } else {\n        dst = size(output.width, (src.height * output.width) / src.width);\n      }\n      break;\n    case \"cover\":\n      if (output.width / output.height > input.width / input.height) {\n        src = size(input.width, (input.width * output.height) / output.width);\n      } else {\n        src = size((input.height * output.width) / output.height, input.height);\n      }\n      dst = output;\n      break;\n    case \"fitWidth\":\n      src = size(input.width, (input.width * output.height) / output.width);\n      dst = size(output.width, (src.height * output.width) / src.width);\n      break;\n    case \"fitHeight\":\n      src = size((input.height * output.width) / output.height, input.height);\n      dst = size((src.width * output.height) / src.height, output.height);\n      break;\n    case \"none\":\n      src = size(\n        Math.min(input.width, output.width),\n        Math.min(input.height, output.height)\n      );\n      dst = src;\n      break;\n    case \"scaleDown\":\n      src = input;\n      dst = input;\n      const aspectRatio = input.width / input.height;\n      if (dst.height > output.height) {\n        dst = size(output.height * aspectRatio, output.height);\n      }\n      if (dst.width > output.width) {\n        dst = size(output.width, output.width / aspectRatio);\n      }\n      break;\n    default:\n      exhaustiveCheck(fit);\n  }\n  return { src, dst };\n};\n\nexport const fitRects = (\n  fit: Fit,\n  rect: SkRect,\n  { x, y, width, height }: SkRect\n) => {\n  \"worklet\";\n  const sizes = applyBoxFit(\n    fit,\n    { width: rect.width, height: rect.height },\n    { width, height }\n  );\n  const src = inscribe(sizes.src, rect);\n  const dst = inscribe(sizes.dst, {\n    x,\n    y,\n    width,\n    height,\n  });\n  return { src, dst };\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AASO,MAAMC,IAAI,GAAGA,CAACC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,KAAK;EAC7C,SAAS;;EACT,OAAO;IAAED,KAAK;IAAEC;EAAO,CAAC;AAC1B,CAAC;AAACC,OAAA,CAAAH,IAAA,GAAAA,IAAA;AAEK,MAAMI,SAAS,GAAGA,CACvBC,GAAW,EACXC,GAAW,KAMR;EACH,SAAS;;EACT,MAAMC,MAAM,GAAGD,GAAG,CAACL,KAAK,GAAGI,GAAG,CAACJ,KAAK;EACpC,MAAMO,MAAM,GAAGF,GAAG,CAACJ,MAAM,GAAGG,GAAG,CAACH,MAAM;EACtC,MAAMO,UAAU,GAAGH,GAAG,CAACI,CAAC,GAAGL,GAAG,CAACK,CAAC,GAAGH,MAAM;EACzC,MAAMI,UAAU,GAAGL,GAAG,CAACM,CAAC,GAAGP,GAAG,CAACO,CAAC,GAAGJ,MAAM;EACzC,OAAO,CAAC;IAAEC;EAAW,CAAC,EAAE;IAAEE;EAAW,CAAC,EAAE;IAAEJ;EAAO,CAAC,EAAE;IAAEC;EAAO,CAAC,CAAC;AACjE,CAAC;AAACL,OAAA,CAAAC,SAAA,GAAAA,SAAA;AAEF,MAAMS,QAAQ,GAAGA,CACf;EAAEZ,KAAK;EAAEC;AAAa,CAAC,EACvBY,IAA6D,KAC1D;EACH,SAAS;;EACT,MAAMC,cAAc,GAAG,CAACD,IAAI,CAACb,KAAK,GAAGA,KAAK,IAAI,GAAG;EACjD,MAAMe,eAAe,GAAG,CAACF,IAAI,CAACZ,MAAM,GAAGA,MAAM,IAAI,GAAG;EACpD,OAAO;IACLQ,CAAC,EAAEI,IAAI,CAACJ,CAAC,GAAGK,cAAc;IAC1BH,CAAC,EAAEE,IAAI,CAACF,CAAC,GAAGI,eAAe;IAC3Bf,KAAK;IACLC;EACF,CAAC;AACH,CAAC;AAED,MAAMe,WAAW,GAAGA,CAACC,GAAQ,EAAEC,KAAW,EAAEC,MAAY,KAAK;EAC3D,SAAS;;EACT,IAAIf,GAAG,GAAGL,IAAI,CAAC,CAAC;IACdM,GAAG,GAAGN,IAAI,CAAC,CAAC;EACd,IACEmB,KAAK,CAACjB,MAAM,IAAI,GAAG,IACnBiB,KAAK,CAAClB,KAAK,IAAI,GAAG,IAClBmB,MAAM,CAAClB,MAAM,IAAI,GAAG,IACpBkB,MAAM,CAACnB,KAAK,IAAI,GAAG,EACnB;IACA,OAAO;MAAEI,GAAG;MAAEC;IAAI,CAAC;EACrB;EACA,QAAQY,GAAG;IACT,KAAK,MAAM;MACTb,GAAG,GAAGc,KAAK;MACXb,GAAG,GAAGc,MAAM;MACZ;IACF,KAAK,SAAS;MACZf,GAAG,GAAGc,KAAK;MACX,IAAIC,MAAM,CAACnB,KAAK,GAAGmB,MAAM,CAAClB,MAAM,GAAGG,GAAG,CAACJ,KAAK,GAAGI,GAAG,CAACH,MAAM,EAAE;QACzDI,GAAG,GAAGN,IAAI,CAAEK,GAAG,CAACJ,KAAK,GAAGmB,MAAM,CAAClB,MAAM,GAAIG,GAAG,CAACH,MAAM,EAAEkB,MAAM,CAAClB,MAAM,CAAC;MACrE,CAAC,MAAM;QACLI,GAAG,GAAGN,IAAI,CAACoB,MAAM,CAACnB,KAAK,EAAGI,GAAG,CAACH,MAAM,GAAGkB,MAAM,CAACnB,KAAK,GAAII,GAAG,CAACJ,KAAK,CAAC;MACnE;MACA;IACF,KAAK,OAAO;MACV,IAAImB,MAAM,CAACnB,KAAK,GAAGmB,MAAM,CAAClB,MAAM,GAAGiB,KAAK,CAAClB,KAAK,GAAGkB,KAAK,CAACjB,MAAM,EAAE;QAC7DG,GAAG,GAAGL,IAAI,CAACmB,KAAK,CAAClB,KAAK,EAAGkB,KAAK,CAAClB,KAAK,GAAGmB,MAAM,CAAClB,MAAM,GAAIkB,MAAM,CAACnB,KAAK,CAAC;MACvE,CAAC,MAAM;QACLI,GAAG,GAAGL,IAAI,CAAEmB,KAAK,CAACjB,MAAM,GAAGkB,MAAM,CAACnB,KAAK,GAAImB,MAAM,CAAClB,MAAM,EAAEiB,KAAK,CAACjB,MAAM,CAAC;MACzE;MACAI,GAAG,GAAGc,MAAM;MACZ;IACF,KAAK,UAAU;MACbf,GAAG,GAAGL,IAAI,CAACmB,KAAK,CAAClB,KAAK,EAAGkB,KAAK,CAAClB,KAAK,GAAGmB,MAAM,CAAClB,MAAM,GAAIkB,MAAM,CAACnB,KAAK,CAAC;MACrEK,GAAG,GAAGN,IAAI,CAACoB,MAAM,CAACnB,KAAK,EAAGI,GAAG,CAACH,MAAM,GAAGkB,MAAM,CAACnB,KAAK,GAAII,GAAG,CAACJ,KAAK,CAAC;MACjE;IACF,KAAK,WAAW;MACdI,GAAG,GAAGL,IAAI,CAAEmB,KAAK,CAACjB,MAAM,GAAGkB,MAAM,CAACnB,KAAK,GAAImB,MAAM,CAAClB,MAAM,EAAEiB,KAAK,CAACjB,MAAM,CAAC;MACvEI,GAAG,GAAGN,IAAI,CAAEK,GAAG,CAACJ,KAAK,GAAGmB,MAAM,CAAClB,MAAM,GAAIG,GAAG,CAACH,MAAM,EAAEkB,MAAM,CAAClB,MAAM,CAAC;MACnE;IACF,KAAK,MAAM;MACTG,GAAG,GAAGL,IAAI,CACRqB,IAAI,CAACC,GAAG,CAACH,KAAK,CAAClB,KAAK,EAAEmB,MAAM,CAACnB,KAAK,CAAC,EACnCoB,IAAI,CAACC,GAAG,CAACH,KAAK,CAACjB,MAAM,EAAEkB,MAAM,CAAClB,MAAM,CACtC,CAAC;MACDI,GAAG,GAAGD,GAAG;MACT;IACF,KAAK,WAAW;MACdA,GAAG,GAAGc,KAAK;MACXb,GAAG,GAAGa,KAAK;MACX,MAAMI,WAAW,GAAGJ,KAAK,CAAClB,KAAK,GAAGkB,KAAK,CAACjB,MAAM;MAC9C,IAAII,GAAG,CAACJ,MAAM,GAAGkB,MAAM,CAAClB,MAAM,EAAE;QAC9BI,GAAG,GAAGN,IAAI,CAACoB,MAAM,CAAClB,MAAM,GAAGqB,WAAW,EAAEH,MAAM,CAAClB,MAAM,CAAC;MACxD;MACA,IAAII,GAAG,CAACL,KAAK,GAAGmB,MAAM,CAACnB,KAAK,EAAE;QAC5BK,GAAG,GAAGN,IAAI,CAACoB,MAAM,CAACnB,KAAK,EAAEmB,MAAM,CAACnB,KAAK,GAAGsB,WAAW,CAAC;MACtD;MACA;IACF;MACE,IAAAC,0BAAe,EAACN,GAAG,CAAC;EACxB;EACA,OAAO;IAAEb,GAAG;IAAEC;EAAI,CAAC;AACrB,CAAC;AAEM,MAAMmB,QAAQ,GAAGA,CACtBP,GAAQ,EACRJ,IAAY,EACZ;EAAEJ,CAAC;EAAEE,CAAC;EAAEX,KAAK;EAAEC;AAAe,CAAC,KAC5B;EACH,SAAS;;EACT,MAAMwB,KAAK,GAAGT,WAAW,CACvBC,GAAG,EACH;IAAEjB,KAAK,EAAEa,IAAI,CAACb,KAAK;IAAEC,MAAM,EAAEY,IAAI,CAACZ;EAAO,CAAC,EAC1C;IAAED,KAAK;IAAEC;EAAO,CAClB,CAAC;EACD,MAAMG,GAAG,GAAGQ,QAAQ,CAACa,KAAK,CAACrB,GAAG,EAAES,IAAI,CAAC;EACrC,MAAMR,GAAG,GAAGO,QAAQ,CAACa,KAAK,CAACpB,GAAG,EAAE;IAC9BI,CAAC;IACDE,CAAC;IACDX,KAAK;IACLC;EACF,CAAC,CAAC;EACF,OAAO;IAAEG,GAAG;IAAEC;EAAI,CAAC;AACrB,CAAC;AAACH,OAAA,CAAAsB,QAAA,GAAAA,QAAA", "ignoreList": []}