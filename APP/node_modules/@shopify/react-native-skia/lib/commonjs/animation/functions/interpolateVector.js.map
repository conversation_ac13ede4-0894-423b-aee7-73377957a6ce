{"version": 3, "names": ["_interpolate", "require", "interpolateVector", "value", "inputRange", "outputRange", "options", "x", "interpolate", "map", "v", "y", "exports", "mixVector", "from", "to"], "sources": ["interpolateVector.ts"], "sourcesContent": ["import type { Vector } from \"../../skia/types\";\n\nimport { interpolate } from \"./interpolate\";\n\nexport const interpolateVector = (\n  value: number,\n  inputRange: readonly number[],\n  outputRange: readonly Vector[],\n  options?: Parameters<typeof interpolate>[3]\n) => {\n  \"worklet\";\n  return {\n    x: interpolate(\n      value,\n      inputRange,\n      outputRange.map((v) => v.x),\n      options\n    ),\n    y: interpolate(\n      value,\n      inputRange,\n      outputRange.map((v) => v.y),\n      options\n    ),\n  };\n};\n\nexport const mixVector = (value: number, from: Vector, to: Vector) => {\n  \"worklet\";\n  return interpolateVector(value, [0, 1], [from, to]);\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,YAAA,GAAAC,OAAA;AAEO,MAAMC,iBAAiB,GAAGA,CAC/BC,KAAa,EACbC,UAA6B,EAC7BC,WAA8B,EAC9BC,OAA2C,KACxC;EACH,SAAS;;EACT,OAAO;IACLC,CAAC,EAAE,IAAAC,wBAAW,EACZL,KAAK,EACLC,UAAU,EACVC,WAAW,CAACI,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACH,CAAC,CAAC,EAC3BD,OACF,CAAC;IACDK,CAAC,EAAE,IAAAH,wBAAW,EACZL,KAAK,EACLC,UAAU,EACVC,WAAW,CAACI,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,CAAC,CAAC,EAC3BL,OACF;EACF,CAAC;AACH,CAAC;AAACM,OAAA,CAAAV,iBAAA,GAAAA,iBAAA;AAEK,MAAMW,SAAS,GAAGA,CAACV,KAAa,EAAEW,IAAY,EAAEC,EAAU,KAAK;EACpE,SAAS;;EACT,OAAOb,iBAAiB,CAACC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAACW,IAAI,EAAEC,EAAE,CAAC,CAAC;AACrD,CAAC;AAACH,OAAA,CAAAC,SAAA,GAAAA,SAAA", "ignoreList": []}