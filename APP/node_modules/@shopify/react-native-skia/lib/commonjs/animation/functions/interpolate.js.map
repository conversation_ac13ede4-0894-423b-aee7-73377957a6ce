{"version": 3, "names": ["Extrapolate", "exports", "getVal", "type", "coef", "val", "leftEdgeOutput", "rightEdgeOutput", "x", "IDENTITY", "CLAMP", "EXTEND", "isExtrapolate", "value", "validateInterpolationOptions", "extrapolationConfig", "extrapolateLeft", "extrapolateRight", "Error", "Object", "assign", "internalInterpolate", "narrowedInput", "leftEdgeInput", "rightEdgeInput", "progress", "interpolate", "input", "output", "length", "i"], "sources": ["interpolate.ts"], "sourcesContent": ["/* eslint-disable max-len */\nexport enum Extrapolate {\n  IDENTITY = \"identity\",\n  CLAMP = \"clamp\",\n  EXTEND = \"extend\",\n}\n\ninterface InterpolationNarrowedInput {\n  leftEdgeInput: number;\n  rightEdgeInput: number;\n  leftEdgeOutput: number;\n  rightEdgeOutput: number;\n}\n\nexport interface ExtrapolationConfig {\n  extrapolateLeft?: Extrapolate | string;\n  extrapolateRight?: Extrapolate | string;\n}\n\ninterface RequiredExtrapolationConfig {\n  extrapolateLeft: Extrapolate;\n  extrapolateRight: Extrapolate;\n}\n\nexport type ExtrapolationType =\n  | ExtrapolationConfig\n  | Extrapolate\n  | string\n  | undefined;\n\nfunction getVal(\n  type: Extrapolate,\n  coef: number,\n  val: number,\n  leftEdgeOutput: number,\n  rightEdgeOutput: number,\n  x: number\n): number {\n  \"worklet\";\n  switch (type) {\n    case Extrapolate.IDENTITY:\n      return x;\n    case Extrapolate.CLAMP:\n      if (coef * val < coef * leftEdgeOutput) {\n        return leftEdgeOutput;\n      }\n      return rightEdgeOutput;\n    case Extrapolate.EXTEND:\n    default:\n      return val;\n  }\n}\n\nfunction isExtrapolate(value: string): value is Extrapolate {\n  \"worklet\";\n  return (\n    value === Extrapolate.EXTEND ||\n    value === Extrapolate.CLAMP ||\n    value === Extrapolate.IDENTITY\n  );\n}\n\n// validates extrapolations type\n// if type is correct, converts it to ExtrapolationConfig\nexport function validateInterpolationOptions(\n  type: ExtrapolationType\n): RequiredExtrapolationConfig {\n  \"worklet\";\n  // initialize extrapolationConfig with default extrapolation\n  const extrapolationConfig: RequiredExtrapolationConfig = {\n    extrapolateLeft: Extrapolate.EXTEND,\n    extrapolateRight: Extrapolate.EXTEND,\n  };\n\n  if (!type) {\n    return extrapolationConfig;\n  }\n\n  if (typeof type === \"string\") {\n    if (!isExtrapolate(type)) {\n      throw new Error(\n        `No supported value for \"interpolate\" \\nSupported values: [\"extend\", \"clamp\", \"identity\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\n Valid example:\n        interpolate(value, [inputRange], [outputRange], \"clamp\")`\n      );\n    }\n    extrapolationConfig.extrapolateLeft = type;\n    extrapolationConfig.extrapolateRight = type;\n    return extrapolationConfig;\n  }\n\n  // otherwise type is extrapolation config object\n  if (\n    (type.extrapolateLeft && !isExtrapolate(type.extrapolateLeft)) ||\n    (type.extrapolateRight && !isExtrapolate(type.extrapolateRight))\n  ) {\n    throw new Error(\n      `No supported value for \"interpolate\" \\nSupported values: [\"extend\", \"clamp\", \"identity\", Extrapolatation.CLAMP, Extrapolatation.EXTEND, Extrapolatation.IDENTITY]\\n Valid example:\n      interpolate(value, [inputRange], [outputRange], {\n        extrapolateLeft: Extrapolation.CLAMP,\n        extrapolateRight: Extrapolation.IDENTITY\n      }})`\n    );\n  }\n\n  Object.assign(extrapolationConfig, type);\n  return extrapolationConfig;\n}\n\nfunction internalInterpolate(\n  x: number,\n  narrowedInput: InterpolationNarrowedInput,\n  extrapolationConfig: RequiredExtrapolationConfig\n) {\n  \"worklet\";\n  const { leftEdgeInput, rightEdgeInput, leftEdgeOutput, rightEdgeOutput } =\n    narrowedInput;\n  if (rightEdgeInput - leftEdgeInput === 0) {\n    return leftEdgeOutput;\n  }\n  const progress = (x - leftEdgeInput) / (rightEdgeInput - leftEdgeInput);\n  const val = leftEdgeOutput + progress * (rightEdgeOutput - leftEdgeOutput);\n  const coef = rightEdgeOutput >= leftEdgeOutput ? 1 : -1;\n\n  if (coef * val < coef * leftEdgeOutput) {\n    return getVal(\n      extrapolationConfig.extrapolateLeft,\n      coef,\n      val,\n      leftEdgeOutput,\n      rightEdgeOutput,\n      x\n    );\n  } else if (coef * val > coef * rightEdgeOutput) {\n    return getVal(\n      extrapolationConfig.extrapolateRight,\n      coef,\n      val,\n      leftEdgeOutput,\n      rightEdgeOutput,\n      x\n    );\n  }\n\n  return val;\n}\n\n// e.g. function interpolate(x, input, output, type = Extrapolatation.CLAMP)\nexport function interpolate(\n  x: number,\n  input: readonly number[],\n  output: readonly number[],\n  type?: ExtrapolationType\n): number {\n  \"worklet\";\n  if (input.length < 2 || output.length < 2) {\n    throw Error(\n      \"Interpolation input and output should contain at least two values.\"\n    );\n  }\n\n  const extrapolationConfig = validateInterpolationOptions(type);\n  const { length } = input;\n  const narrowedInput: InterpolationNarrowedInput = {\n    leftEdgeInput: input[0],\n    rightEdgeInput: input[1],\n    leftEdgeOutput: output[0],\n    rightEdgeOutput: output[1],\n  };\n  if (length > 2) {\n    if (x > input[length - 1]) {\n      narrowedInput.leftEdgeInput = input[length - 2];\n      narrowedInput.rightEdgeInput = input[length - 1];\n      narrowedInput.leftEdgeOutput = output[length - 2];\n      narrowedInput.rightEdgeOutput = output[length - 1];\n    } else {\n      for (let i = 1; i < length; ++i) {\n        if (x <= input[i]) {\n          narrowedInput.leftEdgeInput = input[i - 1];\n          narrowedInput.rightEdgeInput = input[i];\n          narrowedInput.leftEdgeOutput = output[i - 1];\n          narrowedInput.rightEdgeOutput = output[i];\n          break;\n        }\n      }\n    }\n  }\n\n  return internalInterpolate(x, narrowedInput, extrapolationConfig);\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA,IACYA,WAAW,GAAAC,OAAA,CAAAD,WAAA,0BAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAXA,WAAW;EAAA,OAAXA,WAAW;AAAA;AA6BvB,SAASE,MAAMA,CACbC,IAAiB,EACjBC,IAAY,EACZC,GAAW,EACXC,cAAsB,EACtBC,eAAuB,EACvBC,CAAS,EACD;EACR,SAAS;;EACT,QAAQL,IAAI;IACV,KAAKH,WAAW,CAACS,QAAQ;MACvB,OAAOD,CAAC;IACV,KAAKR,WAAW,CAACU,KAAK;MACpB,IAAIN,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;QACtC,OAAOA,cAAc;MACvB;MACA,OAAOC,eAAe;IACxB,KAAKP,WAAW,CAACW,MAAM;IACvB;MACE,OAAON,GAAG;EACd;AACF;AAEA,SAASO,aAAaA,CAACC,KAAa,EAAwB;EAC1D,SAAS;;EACT,OACEA,KAAK,KAAKb,WAAW,CAACW,MAAM,IAC5BE,KAAK,KAAKb,WAAW,CAACU,KAAK,IAC3BG,KAAK,KAAKb,WAAW,CAACS,QAAQ;AAElC;;AAEA;AACA;AACO,SAASK,4BAA4BA,CAC1CX,IAAuB,EACM;EAC7B,SAAS;;EACT;EACA,MAAMY,mBAAgD,GAAG;IACvDC,eAAe,EAAEhB,WAAW,CAACW,MAAM;IACnCM,gBAAgB,EAAEjB,WAAW,CAACW;EAChC,CAAC;EAED,IAAI,CAACR,IAAI,EAAE;IACT,OAAOY,mBAAmB;EAC5B;EAEA,IAAI,OAAOZ,IAAI,KAAK,QAAQ,EAAE;IAC5B,IAAI,CAACS,aAAa,CAACT,IAAI,CAAC,EAAE;MACxB,MAAM,IAAIe,KAAK,CACb;AACR,iEACM,CAAC;IACH;IACAH,mBAAmB,CAACC,eAAe,GAAGb,IAAI;IAC1CY,mBAAmB,CAACE,gBAAgB,GAAGd,IAAI;IAC3C,OAAOY,mBAAmB;EAC5B;;EAEA;EACA,IACGZ,IAAI,CAACa,eAAe,IAAI,CAACJ,aAAa,CAACT,IAAI,CAACa,eAAe,CAAC,IAC5Db,IAAI,CAACc,gBAAgB,IAAI,CAACL,aAAa,CAACT,IAAI,CAACc,gBAAgB,CAAE,EAChE;IACA,MAAM,IAAIC,KAAK,CACb;AACN;AACA;AACA;AACA,UACI,CAAC;EACH;EAEAC,MAAM,CAACC,MAAM,CAACL,mBAAmB,EAAEZ,IAAI,CAAC;EACxC,OAAOY,mBAAmB;AAC5B;AAEA,SAASM,mBAAmBA,CAC1Bb,CAAS,EACTc,aAAyC,EACzCP,mBAAgD,EAChD;EACA,SAAS;;EACT,MAAM;IAAEQ,aAAa;IAAEC,cAAc;IAAElB,cAAc;IAAEC;EAAgB,CAAC,GACtEe,aAAa;EACf,IAAIE,cAAc,GAAGD,aAAa,KAAK,CAAC,EAAE;IACxC,OAAOjB,cAAc;EACvB;EACA,MAAMmB,QAAQ,GAAG,CAACjB,CAAC,GAAGe,aAAa,KAAKC,cAAc,GAAGD,aAAa,CAAC;EACvE,MAAMlB,GAAG,GAAGC,cAAc,GAAGmB,QAAQ,IAAIlB,eAAe,GAAGD,cAAc,CAAC;EAC1E,MAAMF,IAAI,GAAGG,eAAe,IAAID,cAAc,GAAG,CAAC,GAAG,CAAC,CAAC;EAEvD,IAAIF,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGE,cAAc,EAAE;IACtC,OAAOJ,MAAM,CACXa,mBAAmB,CAACC,eAAe,EACnCZ,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH,CAAC,MAAM,IAAIJ,IAAI,GAAGC,GAAG,GAAGD,IAAI,GAAGG,eAAe,EAAE;IAC9C,OAAOL,MAAM,CACXa,mBAAmB,CAACE,gBAAgB,EACpCb,IAAI,EACJC,GAAG,EACHC,cAAc,EACdC,eAAe,EACfC,CACF,CAAC;EACH;EAEA,OAAOH,GAAG;AACZ;;AAEA;AACO,SAASqB,WAAWA,CACzBlB,CAAS,EACTmB,KAAwB,EACxBC,MAAyB,EACzBzB,IAAwB,EAChB;EACR,SAAS;;EACT,IAAIwB,KAAK,CAACE,MAAM,GAAG,CAAC,IAAID,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;IACzC,MAAMX,KAAK,CACT,oEACF,CAAC;EACH;EAEA,MAAMH,mBAAmB,GAAGD,4BAA4B,CAACX,IAAI,CAAC;EAC9D,MAAM;IAAE0B;EAAO,CAAC,GAAGF,KAAK;EACxB,MAAML,aAAyC,GAAG;IAChDC,aAAa,EAAEI,KAAK,CAAC,CAAC,CAAC;IACvBH,cAAc,EAAEG,KAAK,CAAC,CAAC,CAAC;IACxBrB,cAAc,EAAEsB,MAAM,CAAC,CAAC,CAAC;IACzBrB,eAAe,EAAEqB,MAAM,CAAC,CAAC;EAC3B,CAAC;EACD,IAAIC,MAAM,GAAG,CAAC,EAAE;IACd,IAAIrB,CAAC,GAAGmB,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC,EAAE;MACzBP,aAAa,CAACC,aAAa,GAAGI,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MAC/CP,aAAa,CAACE,cAAc,GAAGG,KAAK,CAACE,MAAM,GAAG,CAAC,CAAC;MAChDP,aAAa,CAAChB,cAAc,GAAGsB,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;MACjDP,aAAa,CAACf,eAAe,GAAGqB,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;IACpD,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,EAAE,EAAEC,CAAC,EAAE;QAC/B,IAAItB,CAAC,IAAImB,KAAK,CAACG,CAAC,CAAC,EAAE;UACjBR,aAAa,CAACC,aAAa,GAAGI,KAAK,CAACG,CAAC,GAAG,CAAC,CAAC;UAC1CR,aAAa,CAACE,cAAc,GAAGG,KAAK,CAACG,CAAC,CAAC;UACvCR,aAAa,CAAChB,cAAc,GAAGsB,MAAM,CAACE,CAAC,GAAG,CAAC,CAAC;UAC5CR,aAAa,CAACf,eAAe,GAAGqB,MAAM,CAACE,CAAC,CAAC;UACzC;QACF;MACF;IACF;EACF;EAEA,OAAOT,mBAAmB,CAACb,CAAC,EAAEc,aAAa,EAAEP,mBAAmB,CAAC;AACnE", "ignoreList": []}