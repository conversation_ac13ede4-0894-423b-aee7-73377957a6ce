{"version": 3, "names": ["_typeddash", "require", "_interpolate", "lerp", "value", "from", "to", "p1", "p2", "output", "t", "interpolate", "interpolatePaths", "input", "outputRange", "options", "extrapolation", "validateInterpolationOptions", "extrapolateLeft", "Extrapolate", "CLAMP", "EXTEND", "IDENTITY", "Error", "exhaustiveCheck", "length", "extrapolateRight", "i", "exports"], "sources": ["interpolatePaths.ts"], "sourcesContent": ["import type { SkPath } from \"../../skia/types\";\nimport { exhaustiveCheck } from \"../../renderer/typeddash\";\n\nimport type { ExtrapolationType } from \"./interpolate\";\nimport { validateInterpolationOptions, Extrapolate } from \"./interpolate\";\n\nconst lerp = (\n  value: number,\n  from: number,\n  to: number,\n  p1: SkPath,\n  p2: SkPath,\n  output?: SkPath\n) => {\n  \"worklet\";\n  const t = (value - from) / (to - from);\n  return p2.interpolate(p1, t, output)!;\n};\n\n/**\n * Maps an input value within a range to an output path within a path range.\n * @param value - The input value.\n * @param inputRange - The range of the input value.\n * @param outputRange - The range of the output path.\n * @param options - Extrapolation options\n * @returns The output path.\n * @example <caption>Map a value between 0 and 1 to a path between two paths.</caption>\n * const path1 = new Path();\n * path1.moveTo(0, 0);\n * path1.lineTo(100, 0);\n * const path2 = new Path();\n * path2.moveTo(0, 0);\n * path2.lineTo(0, 100);\n * const path = interpolatePath(0.5, [0, 1], [path1, path2]);\n */\nexport const interpolatePaths = (\n  value: number,\n  input: number[],\n  outputRange: SkPath[],\n  options?: ExtrapolationType,\n  output?: SkPath\n) => {\n  \"worklet\";\n  const extrapolation = validateInterpolationOptions(options);\n  if (value < input[0]) {\n    switch (extrapolation.extrapolateLeft) {\n      case Extrapolate.CLAMP:\n        return outputRange[0];\n      case Extrapolate.EXTEND:\n        return lerp(value, input[0], input[1], outputRange[0], outputRange[1]);\n      case Extrapolate.IDENTITY:\n        throw new Error(\n          \"Identity is not a supported extrapolation type for interpolatePaths()\"\n        );\n      default:\n        exhaustiveCheck(extrapolation.extrapolateLeft);\n    }\n  } else if (value > input[input.length - 1]) {\n    switch (extrapolation.extrapolateRight) {\n      case Extrapolate.CLAMP:\n        return outputRange[outputRange.length - 1];\n      case Extrapolate.EXTEND:\n        return lerp(\n          value,\n          input[input.length - 2],\n          input[input.length - 1],\n          outputRange[input.length - 2],\n          outputRange[input.length - 1]\n        );\n      case Extrapolate.IDENTITY:\n        throw new Error(\n          \"Identity is not a supported extrapolation type for interpolatePaths()\"\n        );\n      default:\n        exhaustiveCheck(extrapolation.extrapolateRight);\n    }\n  }\n  let i = 0;\n  for (; i <= input.length - 1; i++) {\n    if (value >= input[i] && value <= input[i + 1]) {\n      break;\n    }\n  }\n  return lerp(\n    value,\n    input[i],\n    input[i + 1],\n    outputRange[i],\n    outputRange[i + 1],\n    output\n  );\n};\n"], "mappings": ";;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AAGA,IAAAC,YAAA,GAAAD,OAAA;AAEA,MAAME,IAAI,GAAGA,CACXC,KAAa,EACbC,IAAY,EACZC,EAAU,EACVC,EAAU,EACVC,EAAU,EACVC,MAAe,KACZ;EACH,SAAS;;EACT,MAAMC,CAAC,GAAG,CAACN,KAAK,GAAGC,IAAI,KAAKC,EAAE,GAAGD,IAAI,CAAC;EACtC,OAAOG,EAAE,CAACG,WAAW,CAACJ,EAAE,EAAEG,CAAC,EAAED,MAAM,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMG,gBAAgB,GAAGA,CAC9BR,KAAa,EACbS,KAAe,EACfC,WAAqB,EACrBC,OAA2B,EAC3BN,MAAe,KACZ;EACH,SAAS;;EACT,MAAMO,aAAa,GAAG,IAAAC,yCAA4B,EAACF,OAAO,CAAC;EAC3D,IAAIX,KAAK,GAAGS,KAAK,CAAC,CAAC,CAAC,EAAE;IACpB,QAAQG,aAAa,CAACE,eAAe;MACnC,KAAKC,wBAAW,CAACC,KAAK;QACpB,OAAON,WAAW,CAAC,CAAC,CAAC;MACvB,KAAKK,wBAAW,CAACE,MAAM;QACrB,OAAOlB,IAAI,CAACC,KAAK,EAAES,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,EAAEC,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;MACxE,KAAKK,wBAAW,CAACG,QAAQ;QACvB,MAAM,IAAIC,KAAK,CACb,uEACF,CAAC;MACH;QACE,IAAAC,0BAAe,EAACR,aAAa,CAACE,eAAe,CAAC;IAClD;EACF,CAAC,MAAM,IAAId,KAAK,GAAGS,KAAK,CAACA,KAAK,CAACY,MAAM,GAAG,CAAC,CAAC,EAAE;IAC1C,QAAQT,aAAa,CAACU,gBAAgB;MACpC,KAAKP,wBAAW,CAACC,KAAK;QACpB,OAAON,WAAW,CAACA,WAAW,CAACW,MAAM,GAAG,CAAC,CAAC;MAC5C,KAAKN,wBAAW,CAACE,MAAM;QACrB,OAAOlB,IAAI,CACTC,KAAK,EACLS,KAAK,CAACA,KAAK,CAACY,MAAM,GAAG,CAAC,CAAC,EACvBZ,KAAK,CAACA,KAAK,CAACY,MAAM,GAAG,CAAC,CAAC,EACvBX,WAAW,CAACD,KAAK,CAACY,MAAM,GAAG,CAAC,CAAC,EAC7BX,WAAW,CAACD,KAAK,CAACY,MAAM,GAAG,CAAC,CAC9B,CAAC;MACH,KAAKN,wBAAW,CAACG,QAAQ;QACvB,MAAM,IAAIC,KAAK,CACb,uEACF,CAAC;MACH;QACE,IAAAC,0BAAe,EAACR,aAAa,CAACU,gBAAgB,CAAC;IACnD;EACF;EACA,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,IAAId,KAAK,CAACY,MAAM,GAAG,CAAC,EAAEE,CAAC,EAAE,EAAE;IACjC,IAAIvB,KAAK,IAAIS,KAAK,CAACc,CAAC,CAAC,IAAIvB,KAAK,IAAIS,KAAK,CAACc,CAAC,GAAG,CAAC,CAAC,EAAE;MAC9C;IACF;EACF;EACA,OAAOxB,IAAI,CACTC,KAAK,EACLS,KAAK,CAACc,CAAC,CAAC,EACRd,KAAK,CAACc,CAAC,GAAG,CAAC,CAAC,EACZb,WAAW,CAACa,CAAC,CAAC,EACdb,WAAW,CAACa,CAAC,GAAG,CAAC,CAAC,EAClBlB,MACF,CAAC;AACH,CAAC;AAACmB,OAAA,CAAAhB,gBAAA,GAAAA,gBAAA", "ignoreList": []}