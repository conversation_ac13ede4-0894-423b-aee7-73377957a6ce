{"version": 3, "names": ["_math", "require", "_skia", "_interpolate", "interpolateColorsRGB", "value", "inputRange", "outputRange", "r", "interpolate", "map", "c", "g", "b", "a", "interpolateColors", "_outputRange", "cl", "Skia", "Color", "exports", "mixColors", "x", "y", "c1", "c2", "Float32Array", "mix"], "sources": ["interpolateColors.ts"], "sourcesContent": ["import { mix } from \"../../renderer/processors/math\";\nimport type { Color, SkColor } from \"../../skia\";\nimport { Skia } from \"../../skia\";\n\nimport { interpolate } from \"./interpolate\";\n\nconst interpolateColorsRGB = (\n  value: number,\n  inputRange: number[],\n  outputRange: SkColor[]\n) => {\n  \"worklet\";\n  const r = interpolate(\n    value,\n    inputRange,\n    outputRange.map((c) => c[0]),\n    \"clamp\"\n  );\n  const g = interpolate(\n    value,\n    inputRange,\n    outputRange.map((c) => c[1]),\n    \"clamp\"\n  );\n  const b = interpolate(\n    value,\n    inputRange,\n    outputRange.map((c) => c[2]),\n    \"clamp\"\n  );\n  const a = interpolate(\n    value,\n    inputRange,\n    outputRange.map((c) => c[3]),\n    \"clamp\"\n  );\n  // TODO: once Float32Array are supported in the reanimated integration we can switch there\n  return [r, g, b, a];\n};\n\nexport const interpolateColors = (\n  value: number,\n  inputRange: number[],\n  _outputRange: Color[]\n) => {\n  \"worklet\";\n  const outputRange = _outputRange.map((cl) => Skia.Color(cl));\n  return interpolateColorsRGB(value, inputRange, outputRange);\n};\n\nexport const mixColors = (value: number, x: Color, y: Color) => {\n  \"worklet\";\n  const c1 = Skia.Color(x);\n  const c2 = Skia.Color(y);\n  return new Float32Array([\n    mix(value, c1[0], c2[0]),\n    mix(value, c1[1], c2[1]),\n    mix(value, c1[2], c2[2]),\n    mix(value, c1[3], c2[3]),\n  ]);\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,YAAA,GAAAF,OAAA;AAEA,MAAMG,oBAAoB,GAAGA,CAC3BC,KAAa,EACbC,UAAoB,EACpBC,WAAsB,KACnB;EACH,SAAS;;EACT,MAAMC,CAAC,GAAG,IAAAC,wBAAW,EACnBJ,KAAK,EACLC,UAAU,EACVC,WAAW,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B,OACF,CAAC;EACD,MAAMC,CAAC,GAAG,IAAAH,wBAAW,EACnBJ,KAAK,EACLC,UAAU,EACVC,WAAW,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B,OACF,CAAC;EACD,MAAME,CAAC,GAAG,IAAAJ,wBAAW,EACnBJ,KAAK,EACLC,UAAU,EACVC,WAAW,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B,OACF,CAAC;EACD,MAAMG,CAAC,GAAG,IAAAL,wBAAW,EACnBJ,KAAK,EACLC,UAAU,EACVC,WAAW,CAACG,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,EAC5B,OACF,CAAC;EACD;EACA,OAAO,CAACH,CAAC,EAAEI,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC;AACrB,CAAC;AAEM,MAAMC,iBAAiB,GAAGA,CAC/BV,KAAa,EACbC,UAAoB,EACpBU,YAAqB,KAClB;EACH,SAAS;;EACT,MAAMT,WAAW,GAAGS,YAAY,CAACN,GAAG,CAAEO,EAAE,IAAKC,UAAI,CAACC,KAAK,CAACF,EAAE,CAAC,CAAC;EAC5D,OAAOb,oBAAoB,CAACC,KAAK,EAAEC,UAAU,EAAEC,WAAW,CAAC;AAC7D,CAAC;AAACa,OAAA,CAAAL,iBAAA,GAAAA,iBAAA;AAEK,MAAMM,SAAS,GAAGA,CAAChB,KAAa,EAAEiB,CAAQ,EAAEC,CAAQ,KAAK;EAC9D,SAAS;;EACT,MAAMC,EAAE,GAAGN,UAAI,CAACC,KAAK,CAACG,CAAC,CAAC;EACxB,MAAMG,EAAE,GAAGP,UAAI,CAACC,KAAK,CAACI,CAAC,CAAC;EACxB,OAAO,IAAIG,YAAY,CAAC,CACtB,IAAAC,SAAG,EAACtB,KAAK,EAAEmB,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,EACxB,IAAAE,SAAG,EAACtB,KAAK,EAAEmB,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,EACxB,IAAAE,SAAG,EAACtB,KAAK,EAAEmB,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,EACxB,IAAAE,SAAG,EAACtB,KAAK,EAAEmB,EAAE,CAAC,CAAC,CAAC,EAAEC,EAAE,CAAC,CAAC,CAAC,CAAC,CACzB,CAAC;AACJ,CAAC;AAACL,OAAA,CAAAC,SAAA,GAAAA,SAAA", "ignoreList": []}