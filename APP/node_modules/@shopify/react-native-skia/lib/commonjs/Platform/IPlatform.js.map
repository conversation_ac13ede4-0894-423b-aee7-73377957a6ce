{"version": 3, "names": [], "sources": ["IPlatform.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport type { <PERSON>de<PERSON>and<PERSON>, ViewComponent } from \"react-native\";\n\nimport type { DataModule } from \"../skia/types\";\n\nexport interface IPlatform {\n  OS: string;\n  PixelRatio: number;\n  findNodeHandle: (\n    componentOrHandle:\n      | null\n      | number\n      | React.Component<any, any>\n      | React.ComponentClass<any>\n  ) => null | NodeHandle;\n  resolveAsset: (source: DataModule) => string;\n  View: typeof ViewComponent;\n}\n"], "mappings": "", "ignoreList": []}