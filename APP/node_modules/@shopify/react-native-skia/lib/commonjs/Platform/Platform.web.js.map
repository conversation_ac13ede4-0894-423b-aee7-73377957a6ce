{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_types", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "DOM_LAYOUT_HANDLER_NAME", "resizeObserver", "getObserver", "window", "ResizeObserver", "entries", "for<PERSON>ach", "entry", "node", "target", "left", "top", "width", "height", "contentRect", "onLayout", "setTimeout", "timeStamp", "Date", "now", "nativeEvent", "layout", "x", "y", "currentTarget", "bubbles", "cancelable", "defaultPrevented", "eventPhase", "isDefaultPrevented", "Error", "isPropagationStopped", "persist", "preventDefault", "stopPropagation", "isTrusted", "type", "useElementLayout", "ref", "observer", "useLayoutEffect", "current", "observe", "unobserve", "View", "children", "style", "rawStyle", "useMemo", "useRef", "cssStyles", "alignItems", "backgroundColor", "border", "boxSizing", "display", "flexBasis", "flexDirection", "flexShrink", "listStyle", "margin", "minHeight", "min<PERSON><PERSON><PERSON>", "padding", "position", "textDecoration", "zIndex", "createElement", "Platform", "exports", "OS", "PixelRatio", "devicePixelRatio", "resolveAsset", "source", "isRNModule", "getAssetByID", "httpServerLocation", "name", "uri", "findNodeHandle"], "sources": ["Platform.web.tsx"], "sourcesContent": ["import type { RefObject, CSSProperties } from \"react\";\nimport React, { useLayoutEffect, useMemo, useRef } from \"react\";\nimport type { LayoutChangeEvent, ViewComponent, ViewProps } from \"react-native\";\n\nimport type { DataModule } from \"../skia/types\";\nimport { isRNModule } from \"../skia/types\";\n\nimport type { IPlatform } from \"./IPlatform\";\n\n// eslint-disable-next-line max-len\n// https://github.com/necolas/react-native-web/blob/master/packages/react-native-web/src/modules/useElementLayout/index.js\nconst DOM_LAYOUT_HANDLER_NAME = \"__reactLayoutHandler\";\ntype OnLayout = ((event: LayoutChangeEvent) => void) | undefined;\ntype Div = HTMLDivElement & {\n  __reactLayoutHandler: OnLayout;\n};\n\nlet resizeObserver: ResizeObserver | null = null;\n\nconst getObserver = () => {\n  if (resizeObserver == null) {\n    resizeObserver = new window.ResizeObserver(function (entries) {\n      entries.forEach((entry) => {\n        const node = entry.target as Div;\n        const { left, top, width, height } = entry.contentRect;\n        const onLayout = node[DOM_LAYOUT_HANDLER_NAME];\n        if (typeof onLayout === \"function\") {\n          // setTimeout 0 is taken from react-native-web (UIManager)\n          setTimeout(\n            () =>\n              onLayout({\n                timeStamp: Date.now(),\n                nativeEvent: { layout: { x: left, y: top, width, height } },\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-expect-error\n                currentTarget: 0,\n                // eslint-disable-next-line @typescript-eslint/ban-ts-comment\n                // @ts-expect-error\n                target: 0,\n                bubbles: false,\n                cancelable: false,\n                defaultPrevented: false,\n                eventPhase: 0,\n                isDefaultPrevented() {\n                  throw new Error(\"Method not supported on web.\");\n                },\n                isPropagationStopped() {\n                  throw new Error(\"Method not supported on web.\");\n                },\n                persist() {\n                  throw new Error(\"Method not supported on web.\");\n                },\n                preventDefault() {\n                  throw new Error(\"Method not supported on web.\");\n                },\n                stopPropagation() {\n                  throw new Error(\"Method not supported on web.\");\n                },\n                isTrusted: true,\n                type: \"\",\n              }),\n            0\n          );\n        }\n      });\n    });\n  }\n  return resizeObserver;\n};\n\nconst useElementLayout = (ref: RefObject<Div>, onLayout: OnLayout) => {\n  const observer = getObserver();\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node !== null) {\n      node[DOM_LAYOUT_HANDLER_NAME] = onLayout;\n    }\n  }, [ref, onLayout]);\n\n  useLayoutEffect(() => {\n    const node = ref.current;\n    if (node != null && observer != null) {\n      if (typeof node[DOM_LAYOUT_HANDLER_NAME] === \"function\") {\n        observer.observe(node);\n      } else {\n        observer.unobserve(node);\n      }\n    }\n    return () => {\n      if (node != null && observer != null) {\n        observer.unobserve(node);\n      }\n    };\n  }, [observer, ref]);\n};\n\nconst View = (({ children, onLayout, style: rawStyle }: ViewProps) => {\n  const style = useMemo(() => (rawStyle ?? {}) as CSSProperties, [rawStyle]);\n  const ref = useRef<Div>(null);\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  useElementLayout(ref as any, onLayout);\n  const cssStyles = useMemo(() => {\n    return {\n      alignItems: \"stretch\" as const,\n      backgroundColor: \"transparent\" as const,\n      border: \"0 solid black\" as const,\n      boxSizing: \"border-box\" as const,\n      display: \"flex\" as const,\n      flexBasis: \"auto\" as const,\n      flexDirection: \"column\" as const,\n      flexShrink: 0,\n      listStyle: \"none\" as const,\n      margin: 0,\n      minHeight: 0,\n      minWidth: 0,\n      padding: 0,\n      position: \"relative\" as const,\n      textDecoration: \"none\" as const,\n      zIndex: 0,\n      ...style,\n    };\n  }, [style]);\n\n  return (\n    <div ref={ref} style={cssStyles}>\n      {children}\n    </div>\n  );\n}) as unknown as typeof ViewComponent;\n\nexport const Platform: IPlatform = {\n  OS: \"web\",\n  PixelRatio: typeof window !== \"undefined\" ? window.devicePixelRatio : 1, // window is not defined on node\n  resolveAsset: (source: DataModule) => {\n    if (isRNModule(source)) {\n      if (typeof source === \"number\" && typeof require === \"function\") {\n        const {\n          getAssetByID,\n        } = require(\"react-native/Libraries/Image/AssetRegistry\");\n        const { httpServerLocation, name, type } = getAssetByID(source);\n        const uri = `${httpServerLocation}/${name}.${type}`;\n        return uri;\n      }\n      throw new Error(\n        \"Asset source is a number - this is not supported on the web\"\n      );\n    }\n    return source.default;\n  },\n  findNodeHandle: () => {\n    throw new Error(\"findNodeHandle is not supported on the web\");\n  },\n  View,\n};\n"], "mappings": ";;;;;;AACA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AAIA,IAAAC,MAAA,GAAAD,OAAA;AAA2C,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAI3C;AACA;AACA,MAAMW,uBAAuB,GAAG,sBAAsB;AAMtD,IAAIC,cAAqC,GAAG,IAAI;AAEhD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EACxB,IAAID,cAAc,IAAI,IAAI,EAAE;IAC1BA,cAAc,GAAG,IAAIE,MAAM,CAACC,cAAc,CAAC,UAAUC,OAAO,EAAE;MAC5DA,OAAO,CAACC,OAAO,CAAEC,KAAK,IAAK;QACzB,MAAMC,IAAI,GAAGD,KAAK,CAACE,MAAa;QAChC,MAAM;UAAEC,IAAI;UAAEC,GAAG;UAAEC,KAAK;UAAEC;QAAO,CAAC,GAAGN,KAAK,CAACO,WAAW;QACtD,MAAMC,QAAQ,GAAGP,IAAI,CAACR,uBAAuB,CAAC;QAC9C,IAAI,OAAOe,QAAQ,KAAK,UAAU,EAAE;UAClC;UACAC,UAAU,CACR,MACED,QAAQ,CAAC;YACPE,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBC,WAAW,EAAE;cAAEC,MAAM,EAAE;gBAAEC,CAAC,EAAEZ,IAAI;gBAAEa,CAAC,EAAEZ,GAAG;gBAAEC,KAAK;gBAAEC;cAAO;YAAE,CAAC;YAC3D;YACA;YACAW,aAAa,EAAE,CAAC;YAChB;YACA;YACAf,MAAM,EAAE,CAAC;YACTgB,OAAO,EAAE,KAAK;YACdC,UAAU,EAAE,KAAK;YACjBC,gBAAgB,EAAE,KAAK;YACvBC,UAAU,EAAE,CAAC;YACbC,kBAAkBA,CAAA,EAAG;cACnB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;YACjD,CAAC;YACDC,oBAAoBA,CAAA,EAAG;cACrB,MAAM,IAAID,KAAK,CAAC,8BAA8B,CAAC;YACjD,CAAC;YACDE,OAAOA,CAAA,EAAG;cACR,MAAM,IAAIF,KAAK,CAAC,8BAA8B,CAAC;YACjD,CAAC;YACDG,cAAcA,CAAA,EAAG;cACf,MAAM,IAAIH,KAAK,CAAC,8BAA8B,CAAC;YACjD,CAAC;YACDI,eAAeA,CAAA,EAAG;cAChB,MAAM,IAAIJ,KAAK,CAAC,8BAA8B,CAAC;YACjD,CAAC;YACDK,SAAS,EAAE,IAAI;YACfC,IAAI,EAAE;UACR,CAAC,CAAC,EACJ,CACF,CAAC;QACH;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EACA,OAAOnC,cAAc;AACvB,CAAC;AAED,MAAMoC,gBAAgB,GAAGA,CAACC,GAAmB,EAAEvB,QAAkB,KAAK;EACpE,MAAMwB,QAAQ,GAAGrC,WAAW,CAAC,CAAC;EAE9B,IAAAsC,sBAAe,EAAC,MAAM;IACpB,MAAMhC,IAAI,GAAG8B,GAAG,CAACG,OAAO;IACxB,IAAIjC,IAAI,KAAK,IAAI,EAAE;MACjBA,IAAI,CAACR,uBAAuB,CAAC,GAAGe,QAAQ;IAC1C;EACF,CAAC,EAAE,CAACuB,GAAG,EAAEvB,QAAQ,CAAC,CAAC;EAEnB,IAAAyB,sBAAe,EAAC,MAAM;IACpB,MAAMhC,IAAI,GAAG8B,GAAG,CAACG,OAAO;IACxB,IAAIjC,IAAI,IAAI,IAAI,IAAI+B,QAAQ,IAAI,IAAI,EAAE;MACpC,IAAI,OAAO/B,IAAI,CAACR,uBAAuB,CAAC,KAAK,UAAU,EAAE;QACvDuC,QAAQ,CAACG,OAAO,CAAClC,IAAI,CAAC;MACxB,CAAC,MAAM;QACL+B,QAAQ,CAACI,SAAS,CAACnC,IAAI,CAAC;MAC1B;IACF;IACA,OAAO,MAAM;MACX,IAAIA,IAAI,IAAI,IAAI,IAAI+B,QAAQ,IAAI,IAAI,EAAE;QACpCA,QAAQ,CAACI,SAAS,CAACnC,IAAI,CAAC;MAC1B;IACF,CAAC;EACH,CAAC,EAAE,CAAC+B,QAAQ,EAAED,GAAG,CAAC,CAAC;AACrB,CAAC;AAED,MAAMM,IAAI,GAAIA,CAAC;EAAEC,QAAQ;EAAE9B,QAAQ;EAAE+B,KAAK,EAAEC;AAAoB,CAAC,KAAK;EACpE,MAAMD,KAAK,GAAG,IAAAE,cAAO,EAAC,MAAOD,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,CAAC,CAAmB,EAAE,CAACA,QAAQ,CAAC,CAAC;EAC1E,MAAMT,GAAG,GAAG,IAAAW,aAAM,EAAM,IAAI,CAAC;EAC7B;EACAZ,gBAAgB,CAACC,GAAG,EAASvB,QAAQ,CAAC;EACtC,MAAMmC,SAAS,GAAG,IAAAF,cAAO,EAAC,MAAM;IAC9B,OAAO;MACLG,UAAU,EAAE,SAAkB;MAC9BC,eAAe,EAAE,aAAsB;MACvCC,MAAM,EAAE,eAAwB;MAChCC,SAAS,EAAE,YAAqB;MAChCC,OAAO,EAAE,MAAe;MACxBC,SAAS,EAAE,MAAe;MAC1BC,aAAa,EAAE,QAAiB;MAChCC,UAAU,EAAE,CAAC;MACbC,SAAS,EAAE,MAAe;MAC1BC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACXC,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAE,UAAmB;MAC7BC,cAAc,EAAE,MAAe;MAC/BC,MAAM,EAAE,CAAC;MACT,GAAGpB;IACL,CAAC;EACH,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,oBACEtE,MAAA,CAAAU,OAAA,CAAAiF,aAAA;IAAK7B,GAAG,EAAEA,GAAI;IAACQ,KAAK,EAAEI;EAAU,GAC7BL,QACE,CAAC;AAEV,CAAqC;AAE9B,MAAMuB,QAAmB,GAAAC,OAAA,CAAAD,QAAA,GAAG;EACjCE,EAAE,EAAE,KAAK;EACTC,UAAU,EAAE,OAAOpE,MAAM,KAAK,WAAW,GAAGA,MAAM,CAACqE,gBAAgB,GAAG,CAAC;EAAE;EACzEC,YAAY,EAAGC,MAAkB,IAAK;IACpC,IAAI,IAAAC,iBAAU,EAACD,MAAM,CAAC,EAAE;MACtB,IAAI,OAAOA,MAAM,KAAK,QAAQ,IAAI,OAAOhG,OAAO,KAAK,UAAU,EAAE;QAC/D,MAAM;UACJkG;QACF,CAAC,GAAGlG,OAAO,CAAC,4CAA4C,CAAC;QACzD,MAAM;UAAEmG,kBAAkB;UAAEC,IAAI;UAAE1C;QAAK,CAAC,GAAGwC,YAAY,CAACF,MAAM,CAAC;QAC/D,MAAMK,GAAG,GAAG,GAAGF,kBAAkB,IAAIC,IAAI,IAAI1C,IAAI,EAAE;QACnD,OAAO2C,GAAG;MACZ;MACA,MAAM,IAAIjD,KAAK,CACb,6DACF,CAAC;IACH;IACA,OAAO4C,MAAM,CAACxF,OAAO;EACvB,CAAC;EACD8F,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAM,IAAIlD,KAAK,CAAC,4CAA4C,CAAC;EAC/D,CAAC;EACDc;AACF,CAAC", "ignoreList": []}