{"version": 3, "names": ["_reactNative", "require", "_types", "Platform", "exports", "OS", "RNPlatform", "PixelRatio", "get", "resolveAsset", "source", "isRNModule", "Image", "resolveAssetSource", "uri", "default", "findNodeHandle", "View"], "sources": ["Platform.ts"], "sourcesContent": ["import {\n  Image,\n  PixelRatio,\n  Platform as RNPlatform,\n  findNodeHandle,\n  View,\n} from \"react-native\";\n\nimport type { DataModule } from \"../skia/types\";\nimport { isRNModule } from \"../skia/types\";\n\nimport type { IPlatform } from \"./IPlatform\";\n\nexport const Platform: IPlatform = {\n  OS: RNPlatform.OS,\n  PixelRatio: PixelRatio.get(),\n  resolveAsset: (source: DataModule) => {\n    return isRNModule(source)\n      ? Image.resolveAssetSource(source).uri\n      : source.default;\n  },\n  findNodeHandle,\n  View,\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AASA,IAAAC,MAAA,GAAAD,OAAA;AAIO,MAAME,QAAmB,GAAAC,OAAA,CAAAD,QAAA,GAAG;EACjCE,EAAE,EAAEC,qBAAU,CAACD,EAAE;EACjBE,UAAU,EAAEA,uBAAU,CAACC,GAAG,CAAC,CAAC;EAC5BC,YAAY,EAAGC,MAAkB,IAAK;IACpC,OAAO,IAAAC,iBAAU,EAACD,MAAM,CAAC,GACrBE,kBAAK,CAACC,kBAAkB,CAACH,MAAM,CAAC,CAACI,GAAG,GACpCJ,MAAM,CAACK,OAAO;EACpB,CAAC;EACDC,cAAc,EAAdA,2BAAc;EACdC,IAAI,EAAJA;AACF,CAAC", "ignoreList": []}