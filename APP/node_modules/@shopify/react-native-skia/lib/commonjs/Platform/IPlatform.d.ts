import type { <PERSON>de<PERSON>and<PERSON>, ViewComponent } from "react-native";
import type { DataModule } from "../skia/types";
export interface IPlatform {
    OS: string;
    PixelRatio: number;
    findNodeHandle: (componentOrHandle: null | number | React.Component<any, any> | React.ComponentClass<any>) => null | NodeHandle;
    resolveAsset: (source: DataModule) => string;
    View: typeof ViewComponent;
}
